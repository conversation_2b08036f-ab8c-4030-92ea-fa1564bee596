# -*- coding: utf-8 -*-
"""
消息过滤器模块
负责消息的基本过滤和预处理
"""

import logging
from typing import Dict, List
from datetime import datetime

logger = logging.getLogger(__name__)

class MessageFilter:
    """消息过滤器类"""
    
    def __init__(self, config):
        self.config = config
        self.correction_keywords = self._load_correction_keywords()
    
    def _load_correction_keywords(self) -> List[str]:
        """加载更正关键词列表"""
        if self.config:
            return self.config.get("smart_processing", "correction_keywords", [
                "不对", "错了", "不是", "应该是", "我是说", "我说的是",
                "更正", "纠正", "改正", "修改", "搞错了", "打错了"
            ])
        return [
            "不对", "错了", "不是", "应该是", "我是说", "我说的是",
            "更正", "纠正", "改正", "修改", "搞错了", "打错了"
        ]
    
    def is_correction_message(self, content: str) -> bool:
        """检查是否是更正消息"""
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in self.correction_keywords)
    
    def filter_message(self, sender_nickname: str, content: str, msg_type: str) -> bool:
        """过滤消息，决定是否需要处理
        
        Args:
            sender_nickname: 发送者昵称
            content: 消息内容
            msg_type: 消息类型
            
        Returns:
            bool: True表示需要处理，False表示过滤掉
        """
        # 只处理文本消息
        if msg_type != 'text':
            logger.info(f"跳过非文本消息，类型: {msg_type}")
            return False
        
        # 可以添加更多的过滤逻辑
        # 例如：过滤特定关键词、过滤特定用户等
        
        return True