# -*- coding: utf-8 -*-
"""
项目主入口
根据项目说明书要求，负责初始化日志、启动UI和核心服务
"""

import logging
import sys
import threading
from ui import SmartAssistantUI
from utils import setup_logging
from greeting_scheduler import GreetingScheduler

def main():
    """程序主函数"""
    # 初始化日志
    log_queue = setup_logging()
    logger = logging.getLogger("Main")
    logger.info("=====================================")
    logger.info("  程序启动 - wxauto智能微信助手 v2.0")
    logger.info("=====================================")

    # 启动每日问候调度器
    # try:
    #     scheduler = GreetingScheduler(log_queue=log_queue)
    #     scheduler.start()
    #     logger.info("✅ 每日问候调度器已在后台启动")
    # except Exception as e:
    #     logger.error(f"启动每日问候调度器失败: {e}", exc_info=True)


    # 创建并运行UI
    try:
        app = SmartAssistantUI()
        app.run()
    except Exception as e:
        logger.error(f"UI线程出现致命错误: {e}", exc_info=True)
    finally:
        logger.info("程序已退出。")
        # 确保调度器在程序退出时关闭
        # if 'scheduler' in locals() and scheduler.scheduler.running:
        #     scheduler.stop()
        #     logger.info("每日问候调度器已关闭。")

if __name__ == "__main__":
    main() 