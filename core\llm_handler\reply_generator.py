# -*- coding: utf-8 -*-
"""
LLM回复生成器
负责与LLM交互生成回复
"""

import logging
from typing import List, Dict

logger = logging.getLogger(__name__)

class LLMReplyGenerator:
    """LLM回复生成器"""
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
    
    def generate_reply(self, sender_nickname: str, content: str) -> List[str]:
        """生成回复
        
        Args:
            sender_nickname: 发送者昵称
            content: 消息内容
            
        Returns:
            List[str]: 生成的回复列表
        """
        try:
            if not self.llm_client:
                logger.warning("LLM客户端未初始化，跳过回复")
                return []
            
            # 使用sender_nickname作为user_id，确保每个用户有独立的对话上下文
            if hasattr(self.llm_client, 'chat'):
                # Coze客户端直接调用chat方法，传递用户ID
                response = self.llm_client.chat(message=content, user_id=sender_nickname)
                return [response] if response else []
            else:
                # 其他LLM客户端使用原有方法
                messages = [{"role": "user", "content": content}]
                responses = self.llm_client.generate_response(messages, num_choices=1)
                return responses if responses else []
            
        except Exception as e:
            logger.error(f"从LLM获取回复失败: {e}")
            return []
    
    def test_connection(self) -> tuple[bool, str]:
        """测试LLM连接"""
        if not self.llm_client:
            return False, "LLM客户端未初始化"
        
        return self.llm_client.test_connection()
    
    def get_status(self) -> Dict:
        """获取LLM状态"""
        if not self.llm_client:
            return {"status": "未初始化"}
        
        return self.llm_client.get_status()