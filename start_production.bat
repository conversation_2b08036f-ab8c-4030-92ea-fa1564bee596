@echo off
echo ========================================
echo    微信智能助手 - 生产环境启动脚本
echo ========================================
echo.

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo [错误] 虚拟环境不存在！
    echo 请先运行以下命令创建虚拟环境：
    echo python -m venv venv
    echo venv\Scripts\activate
    echo pip install -r requirements.txt
    pause
    exit /b 1
)

:: 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查依赖是否安装
echo [信息] 检查依赖...
python -c "import wxauto, requests" 2>nul
if %errorlevel% neq 0 (
    echo [警告] 依赖包未完全安装，正在安装...
    pip install -r requirements.txt
)

:: 启动主程序
echo [信息] 启动微信智能助手...
echo.
python main.py

:: 如果程序异常退出，暂停以查看错误信息
if %errorlevel% neq 0 (
    echo.
    echo [错误] 程序异常退出！
    pause
) 