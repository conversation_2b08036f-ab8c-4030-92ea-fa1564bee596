# WXAUTO API 验证文档

## 🚨 全局开发规则 🚨

**强制性要求：**
1. **API优先验证原则**：任何涉及wxauto库的开发，必须首先进行实际API验证测试
2. **文档强制参照**：编写代码前必须参照本验证文档，确认API的可用性和正确用法
3. **禁止猜测使用**：严禁基于假设或猜测使用API方法，所有方法必须经过实际验证
4. **搜索需要授权**：如需查找本文档未覆盖的API，必须征得项目负责人同意
5. **验证结果入档**：所有新验证的API结果必须立即更新到本文档
6. **错误案例记录**：记录所有API使用错误案例，避免重复犯错

**违规后果：**
- 因未遵循本规则导致的代码错误，需要承担额外的调试和修复时间
- 重复违规将被要求重新学习本文档

---

# wxauto API 技术文档

## 🚨 重要提醒
**本文档记录的所有API都是经过实际测试验证的，开发时必须严格按照此文档执行，不得随意修改方法名或参数！**

---

## 🚨 重大错误案例记录

### 1. 全局监听模式无限循环问题 ⚠️
**问题描述：** 全局监听模式下，机器人把自己发送的消息当作新消息处理，导致无限循环

**错误原因：**
- `GetNextNewMessage()` 返回的 `chat_name` 字段是**聊天对象**而非**消息发送者**
- 原过滤逻辑：`if chat_name == self.wx.nickname` **完全错误**
- 机器人发送消息后，该消息被误认为是聊天对象发送的

**正确解决方案：**
1. 获取真实发送者：`msg.sender` 或 `msg.who`
2. 正确过滤：`if sender == self.wx.nickname or sender == 'self'`
3. 添加内容特征过滤：检测机器人回复格式
4. 添加去重机制：避免5秒内重复处理相同消息

**验证状态：** ✅ 已修复

### 2. 扣子API历史对话导致的重复回复问题 ⚠️
**问题描述：** 机器人重复发送相同的多风格回复内容，形成无限循环

**错误原因：**
- 扣子API默认配置 `"auto_save_history": True` 会保存对话历史
- 机器人的回复被当作历史对话的一部分
- 当机器人自己的回复被当作新消息处理时，扣子会基于历史返回相同内容

**正确解决方案：**
```python
request_data = {
    "bot_id": self.bot_id,
    "user_id": self.user_id,
    "stream": False,
    "auto_save_history": False,  # ✅ 禁用历史对话，每次独立对话
    "additional_messages": [...]
}
```

**验证状态：** ✅ 已修复

**补充发现：**
- 扣子API的`user_id`参数非常关键，相同`user_id`会被认为是同一用户
- 项目默认使用`user_id: "123123"`，容易导致历史对话混乱
- **解决方案：** 将默认`user_id`改为`"456456"`，避免与其他测试冲突

### 3. 智能消息处理功能重构 ⭐
**问题描述：** 原有的状态机模式过于复杂，容易出现定时器重叠和状态混乱

**重构方案：** 采用简化的时间驱动模式，参考用户提供的策略
- **消息合并延时**：1.5秒（足以容纳追加内容，又不会显得迟钝）
- **最大合并数量**：5条（3-5条，不宜过多避免语义难以还原）
- **响应延迟配置**：单条消息0.5-1秒，多条消息1.5-2秒（自然对话节奏）
- **更正关键词识别**：支持"不是"、"不对"、"应该是"等修正逻辑

**核心逻辑：**
```
用户发送多条消息（间隔<2秒） → 智能合并 → 识别更正语句 → 
合并为完整表达 → 适当延迟 → LLM处理 → 自然回复
```

**示例场景：**
```
用户：我刚刚去了那家火锅店
用户：不是那家 是你上次说的那家  
用户：味道还可以
用户：就是人有点多

系统处理：检测到4条连续消息 → 识别第二条为更正 → 
最终内容："我刚刚去了你上次说的那家火锅店。味道还可以，就是人有点多。"
```

**验证状态：** ✅ 已重构（时间驱动模式）

### 4. 智能处理定时器重叠执行问题 ⚠️
**问题描述：** 智能消息处理中，多个定时器重叠执行导致重复处理和状态混乱

**错误原因：**
- 用户在等待期间发送新消息时，旧定时器未完全清理
- 定时器回调函数没有检查当前状态有效性
- 导致同一消息被多次处理，出现重复确认回复和LLM调用

**正确解决方案：**
```python
def _on_collect_timeout(self, sender_nickname: str):
    # 检查状态是否仍然有效（防止重复执行）
    current_state = self.user_states.get(sender_nickname)
    if current_state != MessageProcessingState.COLLECTING:
        logger.warning(f"状态已变更为 {current_state}，跳过收集超时处理")
        return
    # ... 继续处理
```

**验证状态：** ✅ 已修复

### 5. 扣子API user_id配置问题 ⚠️
**问题描述：** 代码中修改了默认user_id，但实际仍使用config.json中的旧值

**错误原因：**
- 代码修改：`self.user_id = config.get("coze_pat", "user_id", "456456")`
- 但config.json中仍然是：`"user_id": "123123"`
- 配置文件优先级更高，导致修改无效

**正确解决方案：**
- 直接修改config.json：`"user_id": "456456"`
- 确保代码和配置文件的一致性

**验证状态：** ✅ 已修复

### 6. 发送消息API错误使用
**错误用法：** `chat.send_msg()` （小写，不存在）
**正确用法：** `chat.SendMsg()` （大写）
**验证状态：** ✅ 已修复

### 6. 智能合并延时配置化功能 ⭐
**功能描述：** 将固定的8秒智能合并延时改为可配置项，支持UI界面调整

**实现内容：**
- **配置文件支持**：在`config.json`和`config.py`中添加`smart_processing`配置section
- **UI界面集成**：在配置面板中添加智能消息处理配置区域
- **动态配置更新**：支持运行时实时更新延时参数，无需重启程序
- **预设配置模式**：提供快速响应(4秒/5条)、平衡模式(8秒/10条)、深度思考(12秒/15条)三种预设

**配置参数详情：**
```json
"smart_processing": {
  "enable_smart_processing": true,     // 是否启用智能消息处理
  "merge_timeout": 8.0,                // 消息合并等待时间（秒）
  "max_merge_count": 10,               // 最大合并消息数量
  "response_delay_single": [0.5, 0.8], // 单条消息响应延迟范围
  "response_delay_multi": [1.0, 2.5],  // 多条消息响应延迟范围
  "correction_keywords": [...]          // 更正关键词列表
}
```

**UI界面功能：**
- ✅ 启用/禁用智能处理开关
- ✅ 消息合并延时调节（1-30秒，0.5秒递增）
- ✅ 最大合并数量调节（3-20条，1条递增）
- ✅ 三种预设配置快捷按钮
- ✅ 实时状态显示
- ✅ 运行时动态更新支持

**技术实现：**
```python
# SmartReplyHandlerV2中动态加载配置
def load_smart_processing_config(self):
    if self.config:
        self.enable_smart_processing = self.config.get("smart_processing", "enable_smart_processing", True)
        self.MERGE_TIMEOUT = self.config.get("smart_processing", "merge_timeout", 8.0)
        self.MAX_MERGE_COUNT = self.config.get("smart_processing", "max_merge_count", 10)

# 动态配置更新支持
def update_smart_processing_config(self, config_data):
    if "merge_timeout" in config_data:
        self.MERGE_TIMEOUT = config_data["merge_timeout"]
    # ... 其他参数更新
```

**验证状态：** ✅ 已完成并测试通过

**使用建议：**
- **快速响应模式(4秒/5条)**：适合客服场景，快速响应用户
- **平衡模式(8秒/10条)**：适合日常聊天，平衡响应速度和消息完整性
- **深度思考模式(12秒/15条)**：适合复杂对话，给用户充分表达时间

### 7. 发送消息API错误使用

## 📋 目录
1. [监听模式对比](#监听模式对比)
2. [发送消息API](#发送消息api)
3. [已验证的正确用法](#已验证的正确用法)
4. [常见错误避免](#常见错误避免)
5. [开发规范](#开发规范)

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

### AddListenChat方法
```python
wx.AddListenChat(
    nickname: str,     # 必填: 用户昵称
    callback: callable # 必填: 回调函数 callback(msg, chat)
) -> Chat对象
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行
- **✅ 已修复消息合并重复回复问题**：通过定时器ID机制确保多条消息只生成一条回复
- 测试验证：4条消息合并→1条回复，2条消息合并→1条回复，功能完美工作

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

### AddListenChat方法
```python
wx.AddListenChat(
    nickname: str,     # 必填: 用户昵称
    callback: callable # 必填: 回调函数 callback(msg, chat)
) -> Chat对象
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监听模式发送消息
```python
def send_to_user(wx_instance, user_name, message):
    """全局模式发送消息"""
    # 先切换聊天，再发送
    wx_instance.ChatWith(user_name)
    time.sleep(0.1)  # 等待切换完成
    result = wx_instance.SendMsg(msg=message)
    return result
```

---

## 🚨 常见错误避免

### 1. 方法名错误
```python
# ❌ 错误
wx.send_msg()
chat.send_msg()

# ✅ 正确
wx.SendMsg()
chat.SendMsg()
```

### 2. 参数名错误
```python
# ❌ 错误
wx.AddListenChat(who="用户名", callback=callback)

# ✅ 正确
wx.AddListenChat(nickname="用户名", callback=callback)
```

### 3. 发送消息参数错误
```python
# ❌ 错误
wx.SendMsg("消息内容")

# ✅ 正确
wx.SendMsg(msg="消息内容")
wx.SendMsg(msg="消息内容", who="用户名")
```

---

## 🧠 智能消息处理验证记录

### 1. 定时器重叠执行问题
**问题**: 智能处理定时器重复执行，导致状态混乱
**解决**: 添加状态检查，新定时器启动前取消旧定时器
**验证状态**: ✅ 已解决

### 2. UI开关状态同步问题  
**问题**: UI显示已启用，但后端仍使用标准模式
**原因**: 监控未启动时点击开关，命令未发送到队列
**正确顺序**: 启动监控 → 勾选开关 → 测试
**验证状态**: ✅ 已解决

### 3. 消息合并窗口参数优化
**问题**: 1.5秒合并窗口过短，难以触发消息合并
**分析**: 
- 思考时间不足
- 中文输入法打字时间不够  
- 修正删除重打时间不够
- 追加补充内容时间不够

**优化方案**:
```python
# 修改前
self.MERGE_TIMEOUT = 1.5  # 1.5秒收集期

# 第一次优化
self.MERGE_TIMEOUT = 4.0  # 4秒收集期，更符合实际打字习惯

# 第二次优化  
self.MERGE_TIMEOUT = 6.0  # 6秒收集期，便于测试观察

# 第三次优化
self.MERGE_TIMEOUT = 8.0  # 8秒收集期，给用户更充足的思考和输入时间
```

**预期效果**:
```
测试场景:
"你好" → (3秒思考) "在吗" → (2秒补充) "有个问题"
系统将等待8秒收集完整，然后合并为一条消息处理
```
**验证状态**: ✅ 已验证成功

### 4. 智能处理设为默认主方案
**变更**: 将智能消息处理设为默认启用，隐藏UI切换选项
**原因**: 
- 8秒延时参数调整后，功能工作完美
- 消息合并、更正识别等智能功能表现优秀
- 用户反馈积极，建议设为主要处理方案

**实施方案**:
```python
# smart_reply_handler_v2.py
self.enable_smart_processing = True  # 默认启用智能处理

# ui.py - 隐藏UI控件但保留变量
self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用
# 注释掉所有UI相关的LabelFrame、Checkbutton、Label等控件
```

**效果**:
- 用户启动程序后直接使用智能处理功能
- 界面更简洁，减少用户困惑
- 保留代码兼容性，需要时可轻松恢复UI切换

**验证状态**: ✅ 已实施完成

### 5. 监听模式配置移至主界面
**变更**: 将监听模式选择从配置页面移到主控制面板
**原因**: 
- 监听模式是用户最常使用的功能之一
- 在主界面能更方便地切换模式
- 提升用户体验和操作效率

**实施方案**:
```python
# ui.py - 主界面添加监听模式区域
listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
# 单选按钮水平排列，更简洁
radio.pack(side=tk.LEFT, padx=10)
# 添加简化的模式说明
desc_text = "💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话"

# 从配置页面移除监听模式配置区域
# 监听模式配置已移至主界面，此处不再显示
```

**效果**:
- 用户可在主界面直接切换监听模式
- 配置页面更专注于高级配置项
- 界面布局更合理，常用功能前置

**验证状态**: ✅ 已实施完成

**Bug修复**: 
- 解决了变量初始化顺序问题：`AttributeError: 'SmartAssistantUI' object has no attribute 'listen_mode_var'`
- 将`listen_mode_var`和`enable_smart_var`的初始化移到`__init__`方法中，在`create_widgets`之前执行

---

## 📝 开发规范

### 1. API使用前必须检查
```python
# 在使用任何API前，先检查文档中的验证结果
# 不得随意尝试未验证的方法

# 正确的检查方式
if hasattr(wx, 'SendMsg'):
    # 使用已验证的方法
    result = wx.SendMsg(msg="内容")
else:
    logger.error("SendMsg方法不存在")
```

### 2. 错误处理规范
```python
def safe_send_message(chat_obj, message):
    """安全发送消息的标准方法"""
    try:
        if hasattr(chat_obj, 'SendMsg'):
            result = chat_obj.SendMsg(msg=message)
            if result.get('status') == '成功':
                return True
            else:
                logger.error(f"发送失败: {result}")
                return False
        else:
            logger.error("chat对象没有SendMsg方法")
            return False
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

### 3. 代码注释规范
```python
# 在使用wxauto API时，必须注明验证状态
def send_reply(self, chat, message):
    """
    发送回复消息
    
    使用方法: chat.SendMsg(msg="内容")
    验证状态: ✅ 已在test_addlistenchat_send.py中验证成功
    返回格式: {'status': '成功', 'message': 'success', 'data': None}
    """
    return chat.SendMsg(msg=message)
```

---

## 🔍 API方法签名 (已验证)

### SendMsg方法
```python
# WeChat类的SendMsg方法
wx.SendMsg(
    msg: str,                          # 必填: 消息内容
    who: str = None,                   # 可选: 接收者(不填则发给当前聊天)
    clear: bool = True,                # 可选: 发送后清空输入框
    at: Union[str, List[str]] = None,  # 可选: @某人
    exact: bool = False                # 可选: 精确匹配用户名
) -> WxResponse

# Chat类的SendMsg方法  
chat.SendMsg(
    msg: str,          # 必填: 消息内容
    clear: bool = True # 可选: 发送后清空输入框
) -> dict  # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

---

## 📊 测试验证记录

### 测试文件
- `test_send_modes.py` - 基础API测试
- `test_addlistenchat_send.py` - AddListenChat模式完整测试

### 验证结果
| 方法 | 状态 | 返回值 | 备注 |
|-----|------|--------|------|
| `chat.SendMsg(msg="xxx")` | ✅ 成功 | `{'status': '成功'}` | 推荐用法 |
| `wx.SendMsg(msg="xxx", who="xxx")` | ✅ 成功 | `{'status': '成功'}` | 通用用法 |
| `chat.send_msg()` | ❌ 失败 | - | 方法不存在 |
| `msg.quote()` | ❌ 失败 | `{'status': False}` | 无法引用 |

---

## 🎯 最终方案

### smart_reply_handler_v2.py 中的正确实现
```python
def _send_message(self, to_user: str, message: str, chat_obj=None) -> bool:
    """
    发送消息的标准方法
    验证状态: ✅ 已验证
    """
    try:
        if chat_obj and hasattr(chat_obj, 'SendMsg'):
            # AddListenChat模式: 使用chat对象
            result = chat_obj.SendMsg(msg=message)
        else:
            # 全局模式: 使用wx实例
            wx_instance = self.wechat_manager or self.listener.wx
            result = wx_instance.SendMsg(msg=message, who=to_user)
        
        # 检查返回结果
        if isinstance(result, dict) and result.get('status') == '成功':
            logger.info(f"✅ 消息发送成功: {message[:50]}...")
            return True
        else:
            logger.error(f"❌ 消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送消息异常: {e}")
        return False
```

---

## ⚠️ 开发纪律

1. **严禁随意修改API调用方式**
2. **任何新的API使用必须先验证**
3. **发现问题时优先查阅此文档**
4. **测试通过后及时更新文档**
5. **代码review时必须检查API用法**

## 📋 文档更新记录
- 2025-06-22: 创建文档，验证wxauto基础API方法
- 2025-06-23: 新增全局监听和智能处理相关验证
- 2025-06-23: 更新智能消息处理参数优化记录

---

## 🔄 监听模式对比

### 1. 指定用户监听模式 (AddListenChat)
```python
# 正确用法
wx.AddListenChat(nickname="用户名", callback=回调函数)

# 返回值
<wxauto - Chat object("用户名")>

# 特点
- 为每个用户创建独立的子窗口监听
- 回调函数接收: callback(msg, chat)
- msg对象类型: FriendTextMessage, SelfTextMessage, SystemMessage
- chat对象类型: wxauto.wx.Chat
```

### 2. 全局监听模式 (GetNextNewMessage)
```python
# 正确用法
wx.GetNextNewMessage()

# 特点
- 在微信主窗口监听所有消息
- 没有回调函数，需要主动轮询
- 返回格式: {'chat_name': 'xxx', 'chat_type': 'xxx', 'msg': [消息列表]}
```

---

## 📤 发送消息API

### ✅ 已验证的正确方法

#### 1. 使用Chat对象发送 (推荐用于AddListenChat模式)
```python
# 在AddListenChat回调中使用
def on_message(msg, chat):
    result = chat.SendMsg(msg="消息内容")
    # 返回: {'status': '成功', 'message': 'success', 'data': None}
```

#### 2. 使用全局wx实例发送 (适用于所有模式)
```python
# 指定接收者
result = wx.SendMsg(msg="消息内容", who="用户名")
# 返回: {'status': '成功', 'message': 'success', 'data': None}

# 发送给当前聊天对象
result = wx.SendMsg(msg="消息内容")
```

### ❌ 已验证的错误方法
```python
# 这些方法都不存在或不可用
chat.send_msg()      # ❌ 方法不存在
wx.send_msg()        # ❌ 方法不存在  
msg.quote()          # ❌ 返回失败状态
```

---

## 🧪 已验证的正确用法

### AddListenChat模式发送消息
```python
def on_message_callback(msg, chat):
    """AddListenChat模式的消息回调"""
    # 检查消息类型
    if isinstance(msg, FriendTextMessage):
        # 方式1: 使用chat对象发送 (推荐)
        result = chat.SendMsg(msg="回复内容")
        
        # 方式2: 使用全局wx发送 (备选)
        result = wx.SendMsg(msg="回复内容", who=msg.sender)
```

### 全局监