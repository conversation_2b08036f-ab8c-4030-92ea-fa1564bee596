# -*- coding: utf-8 -*-
"""
统一LLM客户端
支持通义千问和扣子两种模式
"""

import logging
from typing import List, Dict, Any, Optional

class UnifiedLLMClient:
    """统一LLM客户端，支持多种后端"""
    
    def __init__(self, config):
        self.logger = logging.getLogger("UnifiedLLMClient")
        self.config = config
        
        # 从新的配置结构中获取当前提供商
        # 鉴于现在只支持硅基流动，直接设置为openai兼容模式
        self.backend_type = "openai" 
        self.current_provider_config = self._get_current_provider_config()
        
        self.client = None
        self._init_backend()
    
    def _get_current_provider_config(self):
        """获取当前提供商配置"""
        providers = self.config.get("llm_providers", "providers", [])
        current_type = self.config.get("llm_providers", "current_provider", "coze")
        for provider in providers:
            if provider.get("type") == current_type:
                self.config.set("llm_providers", "current_provider_config", provider)
                return provider
        self.logger.warning(f"未找到{current_type} LLM提供商配置，请检查config.json")
        return None
    
    def _init_backend(self):
        """初始化后端客户端"""
        try:
            if not self.current_provider_config:
                self.logger.error("未找到LLM提供商配置，无法初始化LLM客户端")
                raise ValueError("未找到LLM提供商配置")
            provider_type = self.current_provider_config.get("type", "coze")
            provider_name = self.current_provider_config.get("name", "Coze")
            if provider_type == "coze":
                from llm_client_coze import CozeLLMClient
                self.client = CozeLLMClient(
                    api_key=self.current_provider_config.get("api_key"),
                    bot_id=self.current_provider_config.get("bot_id"),
                    user_id=self.current_provider_config.get("user_id"),
                    api_url=self.current_provider_config.get("api_url")
                )
                self.logger.info(f"✅ 已初始化 {provider_name} (Coze) 客户端")
            elif provider_type in ("openai", "siliconflow"):
                from llm_client_openai import OpenAICompatibleLLMClient
                self.client = OpenAICompatibleLLMClient(self.config)
                self.logger.info(f"✅ 已初始化 {provider_name} (OpenAI兼容) 客户端")
            else:
                raise ValueError(f"不支持的LLM类型: {provider_type}")
        except ImportError as e:
            self.logger.error(f"导入LLM客户端失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"初始化LLM客户端失败: {e}")
            raise
    
    def generate_response(self, messages: List[Dict[str, str]], temperature: float = 0.7, num_choices: int = 1) -> List[str]:
        """生成回复"""
        if not self.client:
            self.logger.error("LLM客户端未初始化")
            return []
        
        return self.client.generate_response(messages, temperature, num_choices)
    
    def generate_choices(self, context_messages: List[Dict[str, str]], current_message: str, num_choices: int = 3) -> List[str]:
        """生成多个备选回复"""
        if not self.client:
            self.logger.error("LLM客户端未初始化")
            return []
        
        return self.client.generate_choices(context_messages, current_message, num_choices)
    
    def test_connection(self) -> tuple[bool, str]:
        """测试连接"""
        if not self.client:
            return False, "LLM客户端未初始化"
        
        return self.client.test_connection()
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态"""
        if not self.client:
            return {"backend_type": self.backend_type, "status": "未初始化"}
        
        status = self.client.get_status()
        status["backend_type"] = self.backend_type
        return status
    
# 为了保持向后兼容，创建别名
LLMClient = UnifiedLLMClient
LLMClientUnified = UnifiedLLMClient
