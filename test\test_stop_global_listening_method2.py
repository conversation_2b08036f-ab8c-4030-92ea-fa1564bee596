#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本2：程序终结方案停止全局监听
用法：在全局监听运行时执行此脚本，测试程序终结停止方案
"""

import sys
import os
import time
import threading
import psutil
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from wxauto import WeChat
from wechat_listener import WeChatListener
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_method2.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def find_main_process():
    """查找主程序进程"""
    try:
        current_pid = os.getpid()
        current_process = psutil.Process(current_pid)
        
        # 查找可能的主程序进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('main' in arg or 'app' in arg or 'gui' in arg for arg in cmdline):
                        if proc.pid != current_pid:  # 不是当前测试脚本
                            logger.info(f"🔍 找到可能的主程序进程: PID={proc.pid}, CMD={' '.join(cmdline)}")
                            return proc.pid
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return None
    except Exception as e:
        logger.error(f"❌ 查找主程序进程失败: {e}")
        return None

def test_program_termination():
    """测试程序终结方案"""
    try:
        logger.info("🧪 开始测试方案2：程序终结方案")
        
        # 初始化微信实例
        logger.info("📱 初始化微信实例...")
        wx = WeChat()
        
        # 创建监听器实例
        logger.info("🎧 创建监听器实例...")
        listener = WeChatListener(wx)
        
        # 检查当前是否有全局监听在运行
        if not listener.is_listening or listener.listen_mode != "global":
            logger.error("❌ 当前没有全局监听在运行，请先启动全局监听")
            return False
        
        logger.info("✅ 检测到全局监听正在运行")
        logger.info(f"📊 当前监听模式: {listener.listen_mode}")
        logger.info(f"🔄 监听状态: {listener.is_listening}")
        
        # 步骤1：尝试正常停止
        logger.info("🛑 步骤1：尝试正常停止监听...")
        success = listener.stop_listening()
        
        # 等待5秒检查是否停止
        logger.info("⏳ 等待5秒检查停止效果...")
        time.sleep(5)
        
        # 检查全局监听线程状态
        global_thread = getattr(listener, 'global_listener_thread', None)
        if global_thread and global_thread.is_alive():
            logger.warning("⚠️ 正常停止失败，全局监听线程仍在运行")
            
            # 步骤2：程序终结方案
            logger.info("💥 步骤2：执行程序终结方案")
            
            # 保存重要数据（如果有的话）
            logger.info("💾 保存重要数据...")
            try:
                # 这里可以保存统计数据、配置等
                with open('test/emergency_stop_log.txt', 'w', encoding='utf-8') as f:
                    f.write(f"紧急停止时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"监听模式: {listener.listen_mode}\n")
                    f.write(f"线程状态: {'运行中' if global_thread.is_alive() else '已停止'}\n")
                    f.write("原因: 全局监听无法正常停止，执行程序终结\n")
                logger.info("✅ 重要数据已保存")
            except Exception as save_error:
                logger.warning(f"⚠️ 保存数据失败: {save_error}")
            
            # 给用户警告时间
            logger.info("⚠️ 警告：将在5秒后强制终止程序...")
            print("\n⚠️ 警告：检测到全局监听无法正常停止")
            print("💥 将在5秒后强制终止程序以确保停止监听")
            print("📊 测试结果：方案2有效，但需要强制终止程序")
            
            for i in range(5, 0, -1):
                print(f"⏰ {i}秒后终止...")
                time.sleep(1)
            
            # 查找并终止主程序进程
            main_pid = find_main_process()
            if main_pid:
                try:
                    logger.info(f"🎯 终止主程序进程 PID: {main_pid}")
                    main_process = psutil.Process(main_pid)
                    main_process.terminate()
                    
                    # 等待进程终止
                    time.sleep(2)
                    if main_process.is_running():
                        logger.warning("⚠️ 进程未响应terminate，使用kill")
                        main_process.kill()
                    
                    logger.info("✅ 主程序进程已终止")
                    print("✅ 主程序已终止，全局监听已停止")
                    return True
                    
                except Exception as kill_error:
                    logger.error(f"❌ 终止主程序失败: {kill_error}")
            
            # 如果找不到主程序，终止当前进程
            logger.info("💥 执行当前进程终止...")
            print("💥 强制终止当前进程...")
            os._exit(0)  # 强制退出
            
        else:
            logger.info("✅ 正常停止成功，全局监听已停止")
            print("\n✅ 意外结果：正常停止方法竟然成功了！")
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 启动测试脚本2：程序终结停止方案")
    
    try:
        result = test_program_termination()
        
        if result:
            logger.info("🎉 测试结果：方案2 成功！")
        else:
            logger.error("💥 测试结果：方案2 失败！")
            
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断测试")
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试脚本异常: {e}")
        print(f"\n❌ 测试脚本出现异常: {e}")

if __name__ == "__main__":
    main()
