# -*- coding: utf-8 -*-
"""
智能回复处理器 V2 - 基于事件驱动
配合WeChatListener使用，处理实时消息并生成智能回复

这是对外接口模块，负责协调各个子模块工作
"""

import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional

# 导入核心模块
from core.msg_proc.message_filter import MessageFilter
from core.msg_proc.smart_queue import SmartMessageQueue
from core.msg_proc.message_sender import WeChatMessageSender
from core.llm_handler.reply_generator import LLMReplyGenerator
from core.db_ops.db_manager import DatabaseOperations

from wechat_listener import WeChatListener
from utils import retry_on_exception

logger = logging.getLogger(__name__)

class SmartReplyHandlerV2:
    """智能回复处理器 V2 - 事件驱动版本"""
    
    def __init__(self, llm_client, database_manager, wechat_manager=None):
        self.llm_client = llm_client
        self.database_manager = database_manager
        self.wechat_manager = wechat_manager
        
        # 创建监听器 - 传递配置信息
        self.listener = WeChatListener(on_message_callback=self._handle_new_message, config=self.llm_client.config if self.llm_client else None)
        
        # 初始化核心模块
        self.message_filter = MessageFilter(self.llm_client.config if self.llm_client else None)
        self.smart_queue = SmartMessageQueue(self.llm_client.config if self.llm_client else None)
        self.reply_generator = LLMReplyGenerator(self.llm_client)
        self.db_operations = DatabaseOperations(self.database_manager)
        self.message_sender = WeChatMessageSender(self.listener)
        
        # 从配置文件加载智能处理参数
        self.config = self.llm_client.config if self.llm_client else None
        self.enable_smart_processing = self.config.get("smart_processing", "enable_smart_processing", True) if self.config else True
        
        # 状态管理
        self.is_running = False
        self.processing_lock = threading.Lock()
    
    def enable_smart_message_processing(self, enable: bool = True):
        """启用/禁用智能消息处理功能"""
        old_status = self.enable_smart_processing
        self.enable_smart_processing = enable
        logger.info(f"🔧 智能消息处理功能状态变更: {old_status} → {enable}")
        if enable:
            logger.info("✅ 智能消息处理功能已启用 - 将使用消息合并和智能处理逻辑")
        else:
            logger.info("❌ 智能消息处理功能已禁用，使用标准处理模式 - 直接回复每条消息")
    
    def update_smart_processing_config(self, config_data):
        """动态更新智能处理配置"""
        try:
            old_enable = self.enable_smart_processing
            
            # 更新配置参数
            if "enable_smart_processing" in config_data:
                self.enable_smart_processing = config_data["enable_smart_processing"]
            
            # 更新队列配置
            self.smart_queue.update_config(config_data)
                
            logger.info(f"🔧 智能处理配置已动态更新:")
            logger.info(f"   启用状态: {old_enable} → {self.enable_smart_processing}")
            
        except Exception as e:
            logger.error(f"动态更新智能处理配置失败: {e}", exc_info=True)
    
    def initialize(self) -> bool:
        """初始化处理器"""
        try:
            if not self.listener.init_wechat_instance():
                logger.error("监听器初始化失败")
                return False
            
            # 确保微信实例正确传递
            self.wechat_manager = self.listener.wx
            
            # 验证微信实例
            if self.wechat_manager is None:
                logger.error("微信实例为空，初始化失败")
                return False
                
            # 验证微信实例的关键方法
            if not hasattr(self.wechat_manager, 'SendMsg'):
                logger.error("微信实例缺少SendMsg方法，初始化失败")
                return False

            logger.info("智能回复处理器初始化成功")
            return True
        except Exception as e:
            logger.error(f"智能回复处理器初始化失败: {e}")
            return False
    
    def add_monitor_user(self, nickname: str) -> bool:
        """添加监控用户"""
        return self.listener.add_monitor_user(nickname)
    
    def remove_monitor_user(self, nickname: str) -> bool:
        """移除监控用户"""
        return self.listener.remove_monitor_user(nickname)
    
    def start_monitoring(self) -> bool:
        """开始监控"""
        try:
            if self.is_running:
                logger.warning("监控已在运行中")
                return True
            
            if not self.listener.start_listening():
                logger.error("启动监听失败")
                return False
            
            self.is_running = True
            logger.info("智能回复监控已启动")
            return True
        except Exception as e:
            logger.error(f"启动监控失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        try:
            if not self.is_running:
                logger.warning("监控未在运行")
                return True
            
            self.listener.stop_listening()
            self.is_running = False
            logger.info("智能回复监控已停止")
            return True
        except Exception as e:
            logger.error(f"停止监控失败: {e}")
            return False
    
    def _handle_new_message(self, sender_nickname: str, content: str, msg_type: str, msg_obj=None):
        """处理新消息的回调函数"""
        try:
            with self.processing_lock:
                logger.info(f"开始处理来自 [{sender_nickname}] 的消息: {content}")
                logger.debug(f"🔍 当前智能处理状态: {self.enable_smart_processing}")

                # 消息过滤
                if not self.message_filter.filter_message(sender_nickname, content, msg_type):
                    return

                # 存储用户消息
                self.db_operations.store_message(sender_nickname, content, 'user')

                # 保存msg_obj到实例变量，供发送消息时使用
                self.current_msg_obj = msg_obj

                # 根据配置选择处理方式
                if self.enable_smart_processing:
                    logger.info(f"📱 使用智能消息处理模式处理消息")
                    self._smart_handle_message(sender_nickname, content)
                else:
                    logger.info(f"📝 使用标准消息处理模式处理消息")
                    # 使用原有的标准处理方式
                    self._standard_handle_message(sender_nickname, content)

        except Exception as e:
            logger.error(f"处理消息失败: {e}", exc_info=True)
    
    def _standard_handle_message(self, sender_nickname: str, content: str):
        """标准消息处理"""
        try:
            # 直接从LLM获取回复
            replies = self.reply_generator.generate_reply(sender_nickname, content)

            if not replies:
                logger.warning("从LLM获取回复为空，不执行任何操作")
                return

            logger.info(f"LLM返回了 {len(replies)} 条回复")

            # 直接发送LLM返回的回复
            for i, reply in enumerate(replies, 1):
                # 传递msg_obj以便在全局监听模式下使用msg.reply()
                msg_obj = getattr(self, 'current_msg_obj', None)
                success = WeChatMessageSender.send_message(self.wechat_manager, self.listener, sender_nickname, reply, msg_obj)
                if success:
                    self.db_operations.store_message(sender_nickname, reply, 'assistant')
                    logger.info(f"已发送第 {i}/{len(replies)} 条回复: {reply[:50]}...")
                else:
                    logger.error(f"发送第 {i} 条回复失败")
            
            logger.info(f"已完成向 {sender_nickname} 的回复发送")

        except Exception as e:
            logger.error(f"标准消息处理失败: {e}")
    
    def _smart_handle_message(self, sender_nickname: str, content: str):
        """智能消息处理（时间驱动模式）"""
        try:
            current_time = time.time()
            
            # 添加调试日志
            logger.info(f"🔍 [DEBUG] 智能处理消息: {content}")
            logger.info(f"🔍 [DEBUG] 当前队列长度: {len(self.smart_queue.user_messages.get(sender_nickname, []))}")
            logger.info(f"🔍 [DEBUG] 是否有活跃定时器: {sender_nickname in self.smart_queue.user_timers}")
            
            # 检查用户是否正在LLM处理中
            if self.smart_queue.is_user_processing(sender_nickname):
                logger.info(f"🔒 [DEBUG] 用户 {sender_nickname} 正在LLM处理中，忽略新消息: {content}")
                return
            
            # 检查是否是更正消息
            if self.message_filter.is_correction_message(content):
                self.smart_queue.handle_correction(sender_nickname, content, current_time)
                # 重新设置定时器
                self._setup_timer_for_user(sender_nickname, current_time)
                return
            
            # 添加消息到队列
            need_immediate_processing = not self.smart_queue.add_message(sender_nickname, content, current_time)
            
            if need_immediate_processing:
                # 立即处理消息
                self._process_user_messages(sender_nickname)
            else:
                # 设置定时器
                self._setup_timer_for_user(sender_nickname, current_time)
            
            # 更新最后活动时间
            self.smart_queue.update_last_activity(sender_nickname, current_time)
            
            logger.info(f"📝 收到用户 {sender_nickname} 消息，已设置 {self.smart_queue.MERGE_TIMEOUT}秒 合并定时器")
                
        except Exception as e:
            logger.error(f"智能消息处理失败: {e}", exc_info=True)
            # 发生错误时回退到标准处理
            self.smart_queue.reset_user_state(sender_nickname)
            self._standard_handle_message(sender_nickname, content)
    
    def _setup_timer_for_user(self, sender_nickname: str, current_time: float):
        """为用户设置定时器"""
        # 取消之前的定时器
        logger.info(f"🔍 [DEBUG] 准备取消之前的定时器...")
        self.smart_queue.cancel_timer(sender_nickname)
        logger.info(f"🔍 [DEBUG] 定时器取消完成")
        
        # 生成唯一的定时器ID
        timer_id = f"{sender_nickname}_{current_time}"
        
        # 设置新的合并定时器
        timer = threading.Timer(self.smart_queue.MERGE_TIMEOUT, self._process_user_messages, [sender_nickname])
        self.smart_queue.set_timer(sender_nickname, timer, timer_id)
        timer.start()
        logger.info(f"🔍 [DEBUG] 新定时器已启动，ID: {timer_id}")
    
    def _process_user_messages(self, sender_nickname: str):
        """处理用户消息"""
        try:
            logger.info(f"🔍 [DEBUG] _process_user_messages被调用，用户: {sender_nickname}")
            logger.info(f"🔍 [DEBUG] 当前时间: {time.time()}")
            
            # 检查是否还有待处理的消息
            if sender_nickname not in self.smart_queue.user_messages or not self.smart_queue.user_messages[sender_nickname]:
                logger.info(f"🔍 [DEBUG] 没有待处理消息，退出")
                return
                
            messages = self.smart_queue.user_messages[sender_nickname].copy()
            message_count = len(messages)
            
            logger.info(f"🔍 [DEBUG] 找到 {message_count} 条待处理消息")
            for i, msg in enumerate(messages):
                logger.info(f"🔍 [DEBUG] 消息{i+1}: {msg['content']} (时间戳: {msg['timestamp']})")
            
            logger.info(f"🔄 开始处理用户 {sender_nickname} 的 {message_count} 条合并消息")
            
            # 合并消息内容
            combined_content = self.smart_queue.combine_messages(messages)
            
            # 根据消息数量确定响应延迟
            delay = self.smart_queue.get_response_delay(message_count)
            
            logger.info(f"⏰ 设置 {delay:.1f}秒 响应延迟（{message_count}条消息）")
            
            # 设置响应定时器
            response_timer = threading.Timer(delay, self._send_llm_response, [sender_nickname, combined_content])
            response_timer.start()
            
            # 清理用户消息队列和定时器
            self.smart_queue.user_messages[sender_nickname] = []
            self.smart_queue.cancel_timer(sender_nickname)
            
        except Exception as e:
            logger.error(f"处理合并消息失败: {e}", exc_info=True)
            # 回退到标准处理
            self.smart_queue.reset_user_state(sender_nickname)
    
    def _send_llm_response(self, sender_nickname: str, content: str):
        """发送LLM响应"""
        try:
            # 设置LLM处理状态
            self.smart_queue.set_processing_status(sender_nickname, True)
            logger.info(f"🤖 开始为用户 {sender_nickname} 生成LLM回复")
            
            # 生成并发送回复
            self._standard_handle_message(sender_nickname, content)
            
        except Exception as e:
            logger.error(f"发送LLM响应失败: {e}", exc_info=True)
        finally:
            # 清除LLM处理状态
            self.smart_queue.set_processing_status(sender_nickname, False)
    
    def get_status(self) -> Dict:
        """获取处理器状态"""
        return {
            'is_running': self.is_running,
            'listener_status': self.listener.get_status(),
            'llm_available': self.llm_client is not None
        }
    
    def get_message_counts(self) -> Dict[str, int]:
        """获取消息计数"""
        return self.listener.get_message_counts()
    
    def get_monitor_users(self) -> List[str]:
        """获取监控用户列表"""
        return self.listener.get_monitor_users()
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理智能处理相关的定时器
            self.smart_queue.cleanup()
            
            self.stop_monitoring()
            self.listener.cleanup()
            logger.info("智能回复处理器清理完成")
        except Exception as e:
            logger.error(f"清理失败: {e}")