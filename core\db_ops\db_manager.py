# -*- coding: utf-8 -*-
"""
数据库操作管理器
负责与数据库交互，存储消息和语料
"""

import logging
from datetime import datetime
from typing import List, Dict

logger = logging.getLogger(__name__)

class DatabaseOperations:
    """数据库操作管理器"""
    
    def __init__(self, database_manager):
        self.database_manager = database_manager
    
    def store_message(self, sender_nickname: str, content: str, role: str):
        """存储消息到数据库"""
        try:
            if self.database_manager:
                # 确定消息方向
                direction = 'incoming' if role == 'user' else 'outgoing'
                
                # 调用正确的方法名 save_chat_message
                self.database_manager.save_chat_message(
                    timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    chat_partner=sender_nickname,
                    direction=direction,
                    content=content,
                    message_type='text'
                )
        except Exception as e:
            logger.error(f"存储消息失败: {e}", exc_info=True)
    
    def get_chat_history(self, chat_partner: str, limit: int = 10) -> List[Dict]:
        """获取聊天历史"""
        if self.database_manager:
            return self.database_manager.get_chat_history(chat_partner, limit)
        return []