# wxauto智能微信助手

基于wxauto开源版和大型语言模型（LLM）的智能微信自动回复助手，能够自动监控微信消息、生成智能回复并学习用户偏好。

## 📋 项目特性

- 🤖 **智能对话回复** - 基于LLM生成多样化的回复选项
- 📱 **微信自动监控** - 实时监控指定用户的微信消息（基于事件驱动）
- 🧠 **学习用户偏好** - 记录用户选择，不断优化回复质量
- 💾 **本地数据存储** - 使用SQLite存储聊天记录和学习语料
- 🔧 **图形化配置界面** - 友好的tkinter GUI配置界面
- 🛡️ **健壮性设计** - 多线程架构，异常重试机制，长期稳定运行

## 🏗️ 项目架构

```
wx_smart_assistant/
├── main.py                # 程序主入口
├── ui.py                  # GUI用户界面
├── config.py              # 配置管理
├── wechat_listener.py     # 微信监控与自动化 (V2事件驱动)
├── llm_client_unified.py  # 统一LLM客户端
├── llm_client_openai.py   # OpenAI兼容客户端 (支持硅基流动)
├── database_manager.py    # 数据库管理
├── smart_reply_handler_v2.py # 智能回复处理器 (V2)
├── utils.py               # 通用工具函数
├── requirements.txt       # 项目依赖
├── README.md              # 项目说明
└── start_production.bat   # Windows启动脚本
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.9.x
- Windows操作系统
- 微信PC客户端
- wxauto开源版（免费）

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行测试
```bash
python test_framework.py
```

### 4. 启动程序
```bash
python main.py
```

## ⚙️ 配置说明

### 微信配置
- **微信昵称**: 指定要操作的微信实例（多开时使用）
- **微信路径**: 微信客户端安装路径
- **监控用户**: 需要监控消息的用户列表

### LLM配置
项目通过`config.json`中的`llm_providers`部分来统一管理所有大语言模型（LLM）的配置。目前，系统仅支持**硅基流动（SiliconFlow）**作为LLM提供商，它兼容OpenAI的API协议。这使得配置LLM后端变得非常简单。

#### 1. `current_provider`
这个字段目前固定设置为`"openai"`，因为它指示系统使用OpenAI兼容的LLM客户端来与硅基流动平台进行交互。

#### 2. `providers`列表
这是一个列表，包含了当前唯一可用的LLM提供商——硅基流动的详细配置。每个提供商都是一个对象，包含以下字段：
- `type`: 提供商的唯一标识符（目前固定为`"siliconflow"`）。
- `name`: 一个易于理解的名称，如`"硅基流动 (DeepSeek)"`。
- `api_url`: 硅基流动平台的API基础地址（通常是`https://api.siliconflow.cn/v1`）。
- `api_key`: 用于认证的API密钥或Token。请务必妥善保管。
- `model_name`: 你要使用的具体模型名称，例如`"deepseek-ai/DeepSeek-V3"`。

下面是一个配置示例：
```json
{
  "llm_providers": {
    "current_provider": "openai",
    "providers": [
      {
        "type": "siliconflow",
        "name": "硅基流动 (DeepSeek)",
        "api_url": "https://api.siliconflow.cn/v1",
        "api_key": "sk-YOUR_SILICONFLOW_API_KEY",
        "model_name": "deepseek-ai/DeepSeek-V3",
        "description": "SiliconFlow提供的DeepSeek模型"
      }
    ]
  }
}
```

### 提示词配置
项目通过`config.json`中的`prompts`部分来管理LLM的系统提示词，特别是人设指令。这些提示词将影响LLM的回复风格和行为。

在程序运行后，您可以在图形界面（UI）的“配置”选项卡中找到“提示词配置”区域。在这里，您可以直接编辑“人设/系统提示词”文本框中的内容。修改完成后，点击“保存提示词”按钮即可将更改保存到`config.json`文件。系统会在下次启动或LLM重新初始化时加载新的提示词。

下面是一个配置示例（与植发顾问人设相似）：
```json
{
  "prompts": {
    "persona_prompt": """
# 植发服务顾问王老师 - 人设与沟通规范
（此处为您的详细人设和沟通规范，您可以根据需求在此处自由编辑）
"""
  }
}
```

### 运行时配置
- **检查间隔**: 微信消息检查频率（秒）
- **存档间隔**: 聊天记录存档频率（秒）
- **历史限制**: 上下文历史消息数量

## 📖 使用指南

### 1. 首次配置
1. 启动程序后，切换到"配置"选项卡。
2. 确保"LLM后端类型"已设置为"硅基流动 (DeepSeek)"（这是目前唯一支持的类型）。
3. 填写你的**硅基流动API密钥（API Key）**，并确认`api_url`和`model_name`正确无误。
4. 在“提示词配置”区域，您可以根据需要修改“人设/系统提示词”。
5. 设置微信客户端路径（可选）。
6. 在"主控制"选项卡添加需要监控的用户。
7. 点击"保存配置"。

### 2. 启动监控
1. 确保微信PC客户端已登录
2. 在"主控制"选项卡点击"启动监控"
3. 程序将使用事件驱动方式自动监控指定用户的消息

### 3. 智能回复流程
1. 收到监控用户消息
2. 系统将根据配置的人设和对话历史，直接生成并发送回复。
3. 记录聊天历史和用户偏好

### 4. 查看日志
- 切换到"日志"选项卡查看运行状态
- 可以清空日志或保存日志到文件

## 🗂️ 核心模块说明

### Config (配置管理)
- 管理所有配置参数
- 支持JSON格式配置文件
- 提供默认配置和配置验证
- 支持多种LLM后端切换

### WeChatManager (微信管理)
- 封装wxauto开源版操作，提供重试机制
- **支持的功能**：
  - ✅ GetAllMessage() - 获取所有消息
  - ✅ GetNewMessage() - 获取新消息
  - ✅ SendMsg() - 发送文本消息
  - ✅ SendFiles() - 发送文件
  - ✅ ChatWith() - 切换聊天窗口
- **新增监听功能**：
  - ✅ AddListenChat() - 添加监听聊天
  - ✅ RemoveListenChat() - 移除监听聊天
  - ✅ StartListening() - 开始监听
  - ✅ StopListening() - 停止监听
  - ✅ KeepRunning() - 保持运行

### SmartReplyHandler (智能回复处理器)
- 基于事件驱动的消息处理
- 人性化发送节奏，避免风控
- 根据人设提示词和对话历史生成回复
- 批量消息发送逻辑

### LLMClient (LLM客户端)
- **统一客户端** (`llm_client_unified.py`): 系统的核心，负责管理和与LLM后端交互。目前它只支持与硅基流动（兼容OpenAI协议）的API进行通信。
- **具体实现**:
  - `llm_client_openai.py`: 兼容OpenAI标准接口的客户端，用于对接硅基流动等兼容OpenAI API的平台。
- **主要功能**:
  - 支持与硅基流动（兼容OpenAI协议）LLM进行交互。
  - 实现了统一的请求重试和错误处理逻辑。
  - 能够根据指令生成单个高质量回复。

### DatabaseManager (数据库管理)
- SQLite数据库操作
- 聊天记录存档和查询
- 学习语料管理
- # 本地知识库加载 (此功能已移除)

### Utils (工具函数)
- 用户输入解析
- 重试装饰器
- 文本处理工具
- 时间格式化等

## 📊 数据库结构

### chat_archive (聊天存档)
存储所有聊天消息记录，包括用户消息和助手回复。

### learning_corpus (学习语料)
记录用户选择的回复，用于优化LLM回复质量。

### system_status (系统状态)
记录系统运行状态和异常信息。

## 🔧 技术实现

### wxauto开源版功能验证
经过实际测试，wxauto开源版支持以下功能：

**基础功能**：
- ✅ WeChat() - 微信实例初始化
- ✅ ChatWith(nickname) - 切换到指定聊天
- ✅ SendMsg(msg) - 发送文本消息
- ✅ SendFiles(file_path) - 发送文件
- ✅ GetAllMessage() - 获取所有消息
- ✅ GetNewMessage() - 获取新消息

**监听功能**（推荐使用）：
- ✅ AddListenChat(nickname, callback) - 添加监听并设置回调
- ✅ RemoveListenChat(nickname) - 移除监听
- ✅ StartListening() - 开始监听
- ✅ StopListening() - 停止监听
- ✅ KeepRunning() - 保持程序运行

**推荐的监听模式**：
```python
def on_message(msg, chat):
    print(f"收到来自 {chat} 的消息: {msg.content}")
    
wx.AddListenChat(nickname="用户名", callback=on_message)
wx.StartListening()
wx.KeepRunning()  # 保持程序运行
```

### 多线程架构
- 主线程: GUI界面，保持响应性
- 监听线程: 基于事件驱动的微信消息监听
- 处理线程: 智能回复生成和发送
- 队列通信: 线程间安全的消息传递

### 异常处理
- 重试机制: 网络请求和wxauto操作自动重试
- 故障恢复: 微信客户端异常时自动重新初始化
- 状态持久化: 关键状态信息定期保存

### 健壮性保障
- 资源监控: 内存和CPU使用情况监控
- 异常隔离: 单个错误不影响整体运行
- 详细日志: 完整的操作日志和错误记录

## 🛠️ 开发与调试

### 运行测试
`verify_production.py` 脚本提供了一些基础的连接和功能测试，可以在修改代码后运行以确保核心功能正常。
```bash
python verify_production.py
```

### 调试模式
在配置中启用debug模式可以获得更详细的日志输出。

## ⚠️ 注意事项

1. **微信客户端要求**
   - 必须是Windows版微信PC客户端
   - 微信窗口不能被完全遮挡
   - 建议使用稳定版本的微信
   - 使用wxauto开源版（免费），不需要wxautox付费版

2. **API配置**
   - 确保LLM API服务可用
   - 注意API使用限制和费用
   - 建议配置适当的超时时间
   - 确保在`config.json`的`llm_providers`中正确填写了你的API Key/Token。

3. **监听模式优势**
   - 推荐使用AddListenChat事件驱动模式
   - 避免使用轮询GetAllMessage()导致的重复处理
   - 事件驱动性能更好，资源占用更少

4. **数据安全**
   - 聊天记录存储在本地数据库
   - 定期备份重要数据
   - 注意保护API密钥安全

5. **使用规范**
   - 遵守微信使用条款
   - 不要用于商业营销
   - 尊重他人隐私
   - 合理设置消息发送间隔，避免风控

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和服务条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📞 支持

如有问题，请在项目Issue中提出，我们会及时回复。 