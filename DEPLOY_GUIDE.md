# 🚀 微信智能助手 - 生产环境部署指南

## 📦 部署包信息
- **包名**: wx_smart_assistant_deploy_20250622_163225.zip
- **大小**: 17.6MB
- **创建时间**: 2025年6月22日
- **版本**: V2 架构（事件驱动模式）

## 🎯 部署包内容清单

### 核心程序文件 (11个)
- `main.py` - 主入口程序
- `ui.py` - 用户界面
- `smart_reply_handler_v2.py` - 智能回复处理器
- `wechat_listener.py` - 微信监听器
- `llm_client_unified.py` - 统一LLM客户端
- `coze_llm_client_pat.py` - 扣子PAT客户端
- `llm_client_qianwen.py` - 千问客户端
- `database_manager.py` - 数据库管理器
- `config.py` - 配置模块
- `utils.py` - 工具函数

### 配置和数据文件 (4个)
- `config.json` - 主配置文件
- `coze_tokens.json` - 认证令牌（重要！）
- `chat_archive.db` - 聊天记录数据库
- `knowledge.txt` - 知识库文件

### 依赖和资源 (2个)
- `requirements.txt` - Python依赖清单

### 文档 (1个)
- `README.md` - 项目说明文档

## 🔧 生产环境部署步骤

### 第一步：环境准备
1. **安装Python**（推荐3.9或3.10版本）
2. **安装微信PC客户端**并登录目标账号
3. **创建项目目录**
   ```cmd
   mkdir D:\wx_bot
   cd D:\wx_bot
   ```

### 第二步：部署代码
1. **解压部署包**到项目目录
2. **创建虚拟环境**
   ```cmd
   python -m venv venv
   venv\Scripts\activate
   ```
3. **安装依赖**
   ```cmd
   pip install -r requirements.txt
   ```

### 第三步：配置检查
1. **验证config.json**中的配置项
2. **确认coze_tokens.json**包含有效的认证信息
3. **检查knowledge.txt**知识库内容

### 第四步：测试运行
```cmd
python main.py
```

### 第五步：设置为系统服务（推荐）
使用 `nssm` 工具将程序设置为Windows服务，实现：
- 开机自启动
- 后台运行
- 自动重启（程序崩溃时）
- 日志记录

## ⚠️ 重要注意事项

1. **认证文件安全**: `coze_tokens.json` 包含敏感认证信息，请妥善保管
2. **微信账号**: 确保生产环境的微信账号已正确登录
3. **网络连接**: 确保服务器能访问扣子API和千问API
4. **监控设置**: 建议设置日志监控和性能监控
5. **备份策略**: 定期备份 `chat_archive.db` 数据库文件

## 📊 系统要求

- **操作系统**: Windows 10/11
- **Python版本**: 3.9 或 3.10
- **内存**: 最少2GB可用内存
- **磁盘**: 最少1GB可用空间
- **网络**: 稳定的互联网连接

## 🛠️ 故障排除

### 常见问题
1. **微信无法连接**: 确保微信客户端已正常登录
2. **LLM调用失败**: 检查token是否有效，网络是否通畅
3. **数据库错误**: 确保有写入权限，磁盘空间充足

### 日志位置
- 应用日志: 程序运行目录
- 服务日志: nssm配置的日志文件路径

## 📞 支持联系

如有部署问题，请联系开发团队。

---
**部署包生成时间**: 2025年6月22日 16:32:25
**架构版本**: V2 (事件驱动)
**状态**: 生产就绪 ✅ 