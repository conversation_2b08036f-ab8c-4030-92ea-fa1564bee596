#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM配置测试脚本
用于测试各种LLM提供商的配置是否正确
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from llm_client_unified import LLMClientUnified
from llm_client_coze import CozeLLMClient
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_llm_config.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_coze_direct():
    """直接测试Coze客户端"""
    print("🧪 直接测试Coze客户端")
    print("=" * 50)
    
    try:
        # 从配置文件获取Coze配置
        config = Config()
        providers = config.get("llm_providers", "providers", [])
        coze_config = None
        
        for provider in providers:
            if provider.get("type") == "coze":
                coze_config = provider
                break
        
        if not coze_config:
            print("❌ 未找到Coze配置")
            return False
        
        print(f"📋 Coze配置信息:")
        print(f"   - 名称: {coze_config.get('name')}")
        print(f"   - API URL: {coze_config.get('api_url')}")
        print(f"   - Bot ID: {coze_config.get('bot_id')}")
        print(f"   - User ID: {coze_config.get('user_id')}")
        print(f"   - API Key: {coze_config.get('api_key', '')[:10]}...")
        
        # 创建Coze客户端
        coze_client = CozeLLMClient(
            api_key=coze_config.get("api_key"),
            bot_id=coze_config.get("bot_id"),
            user_id=coze_config.get("user_id"),
            api_url=coze_config.get("api_url")
        )
        
        print("\n🔄 开始测试连接...")
        
        # 测试连接
        success, message = coze_client.test_connection()
        if success:
            print(f"✅ Coze连接测试成功: {message}")
        else:
            print(f"❌ Coze连接测试失败: {message}")
            return False
        
        # 测试对话
        print("\n💬 测试对话功能...")
        test_messages = [
            "你好",
            "请介绍一下你自己",
            "1+1等于几？"
        ]
        
        for i, msg in enumerate(test_messages, 1):
            print(f"\n📤 测试消息 {i}: {msg}")
            start_time = time.time()
            
            try:
                response = coze_client.chat(msg, user_id="test_user")
                end_time = time.time()
                
                print(f"📥 AI回复 ({end_time - start_time:.2f}秒): {response}")
                
                if "API错误" in response or "请求失败" in response:
                    print(f"❌ 消息 {i} 测试失败")
                    return False
                else:
                    print(f"✅ 消息 {i} 测试成功")
                    
            except Exception as e:
                print(f"❌ 消息 {i} 测试异常: {e}")
                return False
            
            # 避免请求过快
            time.sleep(1)
        
        print("\n🎉 Coze直接测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ Coze直接测试异常: {e}")
        logger.error(f"Coze直接测试异常: {e}", exc_info=True)
        return False

def test_unified_client():
    """测试统一LLM客户端"""
    print("\n🧪 测试统一LLM客户端")
    print("=" * 50)
    
    try:
        # 加载配置
        config = Config()
        current_provider = config.get("llm_providers", "current_provider", "coze")
        
        print(f"📋 当前LLM提供商: {current_provider}")
        
        # 创建统一客户端
        llm_client = LLMClientUnified(config)
        
        # 获取状态
        status = llm_client.get_status()
        print(f"📊 客户端状态: {status}")
        
        # 测试连接
        print("\n🔄 测试连接...")
        success, message = llm_client.test_connection()
        if success:
            print(f"✅ 统一客户端连接测试成功: {message}")
        else:
            print(f"❌ 统一客户端连接测试失败: {message}")
            return False
        
        # 测试消息生成
        print("\n💬 测试消息生成...")
        test_messages = [
            [{"role": "user", "content": "你好，请简单介绍一下你自己"}],
            [{"role": "user", "content": "请用一句话解释什么是人工智能"}],
            [{"role": "user", "content": "今天天气怎么样？"}]
        ]
        
        for i, messages in enumerate(test_messages, 1):
            print(f"\n📤 测试对话 {i}: {messages[0]['content']}")
            start_time = time.time()
            
            try:
                responses = llm_client.generate_response(messages)
                end_time = time.time()
                
                if responses and len(responses) > 0:
                    print(f"📥 AI回复 ({end_time - start_time:.2f}秒): {responses[0]}")
                    print(f"✅ 对话 {i} 测试成功")
                else:
                    print(f"❌ 对话 {i} 测试失败: 未获得回复")
                    return False
                    
            except Exception as e:
                print(f"❌ 对话 {i} 测试异常: {e}")
                return False
            
            # 避免请求过快
            time.sleep(1)
        
        print("\n🎉 统一LLM客户端测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 统一LLM客户端测试异常: {e}")
        logger.error(f"统一LLM客户端测试异常: {e}", exc_info=True)
        return False

def test_config_loading():
    """测试配置加载"""
    print("🧪 测试配置加载")
    print("=" * 50)
    
    try:
        config = Config()
        
        # 检查LLM提供商配置
        providers = config.get("llm_providers", "providers", [])
        current_provider = config.get("llm_providers", "current_provider", "")
        
        print(f"📋 配置信息:")
        print(f"   - 当前提供商: {current_provider}")
        print(f"   - 提供商数量: {len(providers)}")
        
        if not providers:
            print("❌ 未找到任何LLM提供商配置")
            return False
        
        for i, provider in enumerate(providers, 1):
            print(f"\n📋 提供商 {i}:")
            print(f"   - 名称: {provider.get('name', 'N/A')}")
            print(f"   - 类型: {provider.get('type', 'N/A')}")
            print(f"   - API URL: {provider.get('api_url', 'N/A')}")
            
            # 检查必需字段
            required_fields = ['name', 'type', 'api_url', 'api_key']
            missing_fields = []
            
            for field in required_fields:
                if not provider.get(field):
                    missing_fields.append(field)
            
            if provider.get('type') == 'coze':
                coze_required = ['bot_id', 'user_id']
                for field in coze_required:
                    if not provider.get(field):
                        missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ 缺少必需字段: {', '.join(missing_fields)}")
                return False
            else:
                print(f"   ✅ 配置完整")
        
        print("\n✅ 配置加载测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试异常: {e}")
        logger.error(f"配置加载测试异常: {e}", exc_info=True)
        return False

def main():
    """主函数"""
    print("🚀 LLM配置测试脚本")
    print("🔬 此脚本将测试LLM配置是否正确")
    print("=" * 60)
    
    results = []
    
    # 测试1: 配置加载
    print("\n" + "🔍 测试1: 配置加载".center(60, "="))
    results.append(("配置加载", test_config_loading()))
    
    # 测试2: Coze直接测试
    print("\n" + "🔍 测试2: Coze直接测试".center(60, "="))
    results.append(("Coze直接测试", test_coze_direct()))
    
    # 测试3: 统一客户端测试
    print("\n" + "🔍 测试3: 统一客户端测试".center(60, "="))
    results.append(("统一客户端", test_unified_client()))
    
    # 汇总结果
    print("\n" + "📊 测试结果汇总".center(60, "="))
    all_passed = True
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试通过！LLM配置正确，可以正常使用。")
    else:
        print("💥 部分测试失败！请检查配置和网络连接。")
    
    print(f"\n📋 详细日志已保存到: test/test_llm_config.log")

if __name__ == "__main__":
    main()
