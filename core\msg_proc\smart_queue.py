# -*- coding: utf-8 -*-
"""
智能消息队列管理器
负责管理用户消息队列、定时器和合并逻辑
"""

import logging
import threading
import time
from typing import Dict, List
from datetime import datetime

logger = logging.getLogger(__name__)

class SmartMessageQueue:
    """智能消息队列管理器"""
    
    def __init__(self, config):
        self.config = config
        self._load_config()
        
        # 用户消息队列和状态
        self.user_messages = {}  # 用户消息队列
        self.user_timers = {}    # 用户定时器
        self.user_last_activity = {}  # 用户最后活动时间
        self.user_processing_status = {}  # 用户LLM处理状态
        
    def _load_config(self):
        """从配置加载参数"""
        try:
            if self.config:
                # 读取配置参数
                self.MERGE_TIMEOUT = self.config.get("smart_processing", "merge_timeout", 8.0)
                self.MAX_MERGE_COUNT = self.config.get("smart_processing", "max_merge_count", 10)
                
                # 响应延迟配置
                response_delay_single = self.config.get("smart_processing", "response_delay_single", [0.5, 0.8])
                response_delay_multi = self.config.get("smart_processing", "response_delay_multi", [1.0, 2.5])
                self.RESPONSE_DELAY_SINGLE = tuple(response_delay_single)
                self.RESPONSE_DELAY_MULTI = tuple(response_delay_multi)
            else:
                # 使用默认配置
                self.MERGE_TIMEOUT = 8.0
                self.MAX_MERGE_COUNT = 10
                self.RESPONSE_DELAY_SINGLE = (0.5, 0.8)
                self.RESPONSE_DELAY_MULTI = (1.0, 2.5)
                
            logger.info(f"🔧 智能队列配置已加载: 延时={self.MERGE_TIMEOUT}秒, 最大={self.MAX_MERGE_COUNT}条")
            
        except Exception as e:
            logger.error(f"加载智能队列配置失败: {e}", exc_info=True)
            # 使用默认值
            self.MERGE_TIMEOUT = 8.0
            self.MAX_MERGE_COUNT = 10
            self.RESPONSE_DELAY_SINGLE = (0.5, 0.8)
            self.RESPONSE_DELAY_MULTI = (1.0, 2.5)
    
    def update_config(self, config_data: Dict):
        """动态更新配置"""
        try:
            old_timeout = self.MERGE_TIMEOUT
            old_count = self.MAX_MERGE_COUNT
            
            # 更新配置参数
            if "merge_timeout" in config_data:
                self.MERGE_TIMEOUT = config_data["merge_timeout"]
            if "max_merge_count" in config_data:
                self.MAX_MERGE_COUNT = config_data["max_merge_count"]
                
            logger.info(f"🔧 智能队列配置已动态更新:")
            logger.info(f"   合并延时: {old_timeout}秒 → {self.MERGE_TIMEOUT}秒")
            logger.info(f"   最大数量: {old_count}条 → {self.MAX_MERGE_COUNT}条")
            
        except Exception as e:
            logger.error(f"动态更新智能队列配置失败: {e}", exc_info=True)
    
    def add_message(self, sender_nickname: str, content: str, timestamp: float = None) -> bool:
        """添加消息到队列
        
        Args:
            sender_nickname: 用户昵称
            content: 消息内容
            timestamp: 时间戳
            
        Returns:
            bool: True表示成功添加，False表示需要立即处理
        """
        if timestamp is None:
            timestamp = time.time()
            
        if sender_nickname not in self.user_messages:
            self.user_messages[sender_nickname] = []
        
        # 检查是否超过最大合并数量
        if len(self.user_messages[sender_nickname]) >= self.MAX_MERGE_COUNT:
            # 需要立即处理已有消息
            return False
        
        self.user_messages[sender_nickname].append({
            "content": content,
            "timestamp": timestamp
        })
        
        logger.info(f"📝 用户 {sender_nickname} 消息队列长度: {len(self.user_messages[sender_nickname])}")
        return True
    
    def handle_correction(self, sender_nickname: str, content: str, timestamp: float = None):
        """处理更正消息"""
        if timestamp is None:
            timestamp = time.time()
            
        logger.info(f"🔧 检测到用户 {sender_nickname} 的更正消息: {content}")
        
        # 如果有待处理的消息，替换最后一条
        if sender_nickname in self.user_messages and self.user_messages[sender_nickname]:
            # 移除最后一条消息
            self.user_messages[sender_nickname].pop()
            logger.info(f"🗑️ 已移除用户 {sender_nickname} 的上一条消息")
        
        # 添加更正后的消息
        self.add_message(sender_nickname, content, timestamp)
    
    def get_messages_for_processing(self, sender_nickname: str) -> List[Dict]:
        """获取需要处理的消息列表"""
        if sender_nickname in self.user_messages:
            messages = self.user_messages[sender_nickname]
            # 清空队列
            self.user_messages[sender_nickname] = []
            return messages
        return []
    
    def get_response_delay(self, message_count: int) -> float:
        """根据消息数量获取响应延迟时间"""
        if message_count == 1:
            delay_range = self.RESPONSE_DELAY_SINGLE
        else:
            delay_range = self.RESPONSE_DELAY_MULTI
        
        import random
        return random.uniform(delay_range[0], delay_range[1])
    
    def combine_messages(self, messages: List[Dict]) -> str:
        """合并消息内容"""
        if not messages:
            return ""
        
        if len(messages) == 1:
            return messages[0]["content"]
        
        # 按时间戳排序
        sorted_messages = sorted(messages, key=lambda x: x["timestamp"])
        
        # 智能合并：根据消息内容判断是否需要标点符号
        combined_parts = []
        for msg in sorted_messages:
            content = msg["content"].strip()
            if content:
                combined_parts.append(content)
        
        # 使用适当的连接符合并
        if len(combined_parts) <= 2:
            # 短句直接连接
            combined = "。".join(combined_parts) if combined_parts else ""
        else:
            # 长句用逗号连接
            combined = "，".join(combined_parts[:-1]) + "。" + combined_parts[-1] if combined_parts else ""
        
        logger.info(f"📄 合并了 {len(messages)} 条消息: {combined[:100]}...")
        return combined
    
    def set_processing_status(self, sender_nickname: str, status: bool):
        """设置用户处理状态"""
        self.user_processing_status[sender_nickname] = status
        if status:
            logger.info(f"🔒 [DEBUG] 设置用户 {sender_nickname} 为LLM处理中状态")
        else:
            logger.info(f"🔓 [DEBUG] 清除用户 {sender_nickname} 的LLM处理状态")
    
    def is_user_processing(self, sender_nickname: str) -> bool:
        """检查用户是否正在处理中"""
        return self.user_processing_status.get(sender_nickname, False)
    
    def update_last_activity(self, sender_nickname: str, timestamp: float = None):
        """更新用户最后活动时间"""
        if timestamp is None:
            timestamp = time.time()
        self.user_last_activity[sender_nickname] = timestamp
    
    def get_last_activity(self, sender_nickname: str) -> float:
        """获取用户最后活动时间"""
        return self.user_last_activity.get(sender_nickname, 0)
    
    def set_timer(self, sender_nickname: str, timer: threading.Timer, timer_id: str):
        """设置用户定时器"""
        self.user_timers[sender_nickname] = {"timer": timer, "timer_id": timer_id}
    
    def get_timer_id(self, sender_nickname: str) -> str:
        """获取用户定时器ID"""
        if sender_nickname in self.user_timers:
            return self.user_timers[sender_nickname]["timer_id"]
        return ""
    
    def cancel_timer(self, sender_nickname: str):
        """取消用户定时器"""
        if sender_nickname in self.user_timers:
            timer = self.user_timers[sender_nickname]["timer"]
            if timer and timer.is_alive():
                timer.cancel()
            del self.user_timers[sender_nickname]
    
    def reset_user_state(self, sender_nickname: str):
        """重置用户状态"""
        self.cancel_timer(sender_nickname)
        self.user_messages.pop(sender_nickname, None)
        self.user_last_activity.pop(sender_nickname, None)
        self.user_processing_status.pop(sender_nickname, None)
        logger.info(f"🔄 已重置用户 {sender_nickname} 的状态")
    
    def cleanup(self):
        """清理所有资源"""
        logger.info("🧹 清理智能消息队列资源...")
        
        # 取消所有定时器
        for sender_nickname in list(self.user_timers.keys()):
            self.cancel_timer(sender_nickname)
        
        # 清理状态
        self.user_messages.clear()
        self.user_last_activity.clear()
        self.user_processing_status.clear()
        
        logger.info("✅ 智能消息队列资源清理完成")