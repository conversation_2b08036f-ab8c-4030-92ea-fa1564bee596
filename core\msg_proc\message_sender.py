# -*- coding: utf-8 -*-
"""
微信消息发送器
负责发送消息到微信
"""

import logging
import time
from typing import Dict

logger = logging.getLogger(__name__)

class WeChatMessageSender:
    """微信消息发送器"""
    
    def __init__(self, listener):
        self.listener = listener
    
    @staticmethod
    def send_message(wechat_manager, listener, to_user: str, message: str) -> bool:
        """发送消息 - 根据监听模式使用不同的发送方式"""
        try:
            # 获取当前监听模式
            listen_mode = listener.get_listen_mode()
            
            if listen_mode == "specific":
                # 指定用户监听模式：在子窗口中直接发送消息
                wx_instance = getattr(wechat_manager, 'wechat_manager', None) or listener.wx
                if wx_instance is None:
                    logger.error("微信实例不可用，无法发送消息")
                    return False
                    
                # 在指定用户模式下，使用who参数发送消息
                result = wx_instance.SendMsg(msg=message, who=to_user)
                
                # 检查返回结果
                if isinstance(result, dict) and result.get('status') == '成功':
                    logger.debug(f"✅ [指定模式] 消息发送成功: {message[:50]}...")
                    return True
                else:
                    logger.error(f"❌ [指定模式] 消息发送失败: {result}")
                    return False
                
            elif listen_mode == "global":
                # 全局监听模式：直接使用SendMsg(who=用户名)方式发送
                wx_instance = getattr(wechat_manager, 'wechat_manager', None) or listener.wx
                if wx_instance is None:
                    logger.error("微信实例不可用，无法发送消息")
                    return False
                    
                # 验证微信实例是否有必要的方法
                if not hasattr(wx_instance, 'SendMsg'):
                    logger.error("微信实例缺少SendMsg方法，无法发送消息")
                    return False
                
                # 根据官方文档推荐：直接使用SendMsg(msg="内容", who="用户名")
                result = wx_instance.SendMsg(msg=message, who=to_user)
                
                # 检查返回结果
                if isinstance(result, dict) and result.get('status') == '成功':
                    logger.debug(f"✅ [全局模式] 消息发送成功 -> {to_user}: {message[:50]}...")
                    return True
                else:
                    logger.error(f"❌ [全局模式] 消息发送失败 -> {to_user}: {result}")
                    return False
            else:
                logger.error(f"未知的监听模式: {listen_mode}")
                return False
                
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False