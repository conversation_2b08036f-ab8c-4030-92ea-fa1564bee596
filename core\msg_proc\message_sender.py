# -*- coding: utf-8 -*-
"""
微信消息发送器
负责发送消息到微信
"""

import logging
import time
from typing import Dict

logger = logging.getLogger(__name__)

class WeChatMessageSender:
    """微信消息发送器"""
    
    def __init__(self, listener):
        self.listener = listener
    
    @staticmethod
    def send_message(wechat_manager, listener, to_user: str, message: str, msg_obj=None) -> bool:
        """发送消息 - 根据监听模式使用不同的发送方式"""
        try:
            # 获取当前监听模式
            listen_mode = listener.get_listen_mode()

            if listen_mode == "specific":
                # 指定用户监听模式：在子窗口中直接发送消息
                wx_instance = getattr(wechat_manager, 'wechat_manager', None) or listener.wx
                if wx_instance is None:
                    logger.error("微信实例不可用，无法发送消息")
                    return False

                # 在指定用户模式下，使用who参数发送消息
                result = wx_instance.SendMsg(msg=message, who=to_user)

                # 检查返回结果
                if isinstance(result, dict) and result.get('status') == '成功':
                    logger.debug(f"✅ [指定模式] 消息发送成功: {message[:50]}...")
                    return True
                else:
                    logger.error(f"❌ [指定模式] 消息发送失败: {result}")
                    return False

            elif listen_mode == "global":
                # 全局监听模式：只使用msg.reply()方法，绝不使用SendMsg避免破坏全局监听
                if msg_obj and hasattr(msg_obj, 'reply'):
                    # 使用官方推荐的msg.reply()方法
                    try:
                        result = msg_obj.reply(message)
                        logger.debug(f"✅ [全局模式] 使用msg.reply()发送成功: {message[:50]}...")
                        return True
                    except Exception as reply_error:
                        logger.error(f"❌ [全局模式] msg.reply()发送失败: {reply_error}")
                        return False
                else:
                    logger.error("❌ [全局模式] 缺少msg对象或reply方法，无法发送消息")
                    logger.error("💡 全局监听模式必须使用msg.reply()方法，不能使用SendMsg")
                    return False
            else:
                logger.error(f"未知的监听模式: {listen_mode}")
                return False

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False