# -*- coding: utf-8 -*-
"""
智能回复处理器 V2 - 基于事件驱动
配合WeChatListener使用，处理实时消息并生成智能回复

这是对外接口模块，负责协调各个子模块工作
"""

import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional

# 导入重构后的核心模块
from core.smart_reply_handler_v2_refactored import SmartReplyHandlerV2

# 为了保持向后兼容，创建别名
SmartReplyHandlerV2 = SmartReplyHandlerV2

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    pass 