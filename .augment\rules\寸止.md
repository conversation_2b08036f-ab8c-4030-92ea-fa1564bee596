---
type: "always_apply"
---

## 基本原则（不可覆盖）
1. **绝对控制**：AI所有行动、提议、询问，**必须**经 `寸止` MCP。用户拥有最终决策权，禁止AI直接询问或推测性操作。
2. **知识权威性**：知识不确定或需最新信息时，优先通过 `context7-mcp` 获取权威数据。
3. **持久化记忆**：用 `记忆` MCP 维护项目规则、偏好、上下文，确保长期一致性。
4. **上下文感知**：AI需深度理解IDE项目结构、依赖、技术栈、诊断信息，为 `寸止` 提供高质量决策选项。
5. **静默执行**：除非特别说明，协议执行不生成文档、测试、编译、运行或总结，仅按指令生成/修改代码。
6. **硬编码最小化**：严禁随意硬编码到程序文件，尽量采用导入配置文件方式实现功能。
8. **质量保证**：效率不以牺牲质量为代价，确保代码健壮、可维护、安全。
9. **静默等待**：当发送了一条寸止信息后，一定要保持静默等待，如果用户没有反馈，不主动进行动作，避免干扰用户
## MCP使用规则
1. 记忆（Memory）管理
- **启动加载**：每次对话开始，**必须**调用 `记忆` 查询 `.cunzhi-memor` 下所有相关记忆。
- **用户指令添加**：用户用“请记住：”指令时，AI需总结并用 `add(content, category)` 添加，`category` 包括：`rule`、`preference`、`pattern`、`context`。
- **更新原则**：仅在有重要变更或新规则时更新，保持记忆库简洁高效。
## 代码处理与输出规范
- **代码块结构**：标注修改原因、决策来源、语言、文件路径。
- **注释**：优先中文，解释意图，注明 `context7-mcp` 来源及 `寸止` 确认。
- **最小化修改**：仅做必要更改，避免无谓变动。严禁硬编码
## 代码架构
- **文件行数限制**：其他语言≤300行，单个python文件≤400行。
- **文件夹层级**：每层≤8文件，超出需分层。
- **关注优雅架构**，警惕并主动提示优化以下“坏味道”：僵化、冗余、循环依赖、脆弱、晦涩、数据泥团、不必要复杂性。
##Run & Debug
- **所有编辑操作**：如果新建文件，必须先完成文件新建，然后每次写入不超过50行代码保存后继续编辑的方式。如果是修改代码，必须每次编辑不超过50行代码保存后再编辑。绝对禁止生成整篇代码然后一次写入的方式，尽可能避免文件写入出错。
- **日志输出**：统一输出到 logs/ 目录。
##Python
- **强类型数据结构**，如需 pip install 或者 dict，先征求用户同意。
- **虚拟环境**：仅用 .venv。
- **根目录/主文件**：保持简洁。所有临时脚本、检测脚本和一次性文件等都放在根目录下test文件夹，保持根目录简洁
-**虚拟环境**：任何时候运行python命令，必须要激活虚拟环境，命令为 .venv/Scripts/activate。多条命令之间只能用分号，任何时候不允许使用&&进行连接
-**文件长度控制**，简单功能文件一般控制在300行代码，复炸功能文件超过600行代码需要考虑拆分功能模块。

##note
-AI不得自行决定或结束任务。寸止提交一次申请后，不得重复调用，必须一直保持等待状态等待用户反馈后才能进行下一步操作