#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的COM全局监听实现
用法：运行此脚本测试新的COM全局监听是否能正常启动和停止
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from wxauto import WeChat
from wechat_listener import WeChatListener
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_new_com.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_message_callback(sender, content, msg_type, msg_obj=None):
    """测试消息回调函数"""
    logger.info(f"📨 收到测试消息 - 发送者: {sender}, 类型: {msg_type}, 内容: {content[:50]}...")
    print(f"📨 测试消息: [{sender}] {content[:50]}...")

def test_new_com_implementation():
    """测试新的COM实现"""
    try:
        logger.info("🧪 开始测试新的COM全局监听实现")
        print("🧪 测试新的COM全局监听实现")
        print("=" * 60)
        
        # 创建监听器
        logger.info("🎧 创建WeChatListener实例...")
        listener = WeChatListener(on_message_callback=test_message_callback)
        
        # 初始化微信实例
        logger.info("📱 初始化微信实例...")
        print("📱 正在初始化微信实例...")
        if not listener.init_wechat_instance():
            logger.error("❌ 微信实例初始化失败")
            print("❌ 微信实例初始化失败")
            return False
        
        print("✅ 微信实例初始化成功")
        
        # 设置全局监听模式
        logger.info("🌐 设置全局监听模式...")
        print("🌐 设置全局监听模式...")
        if not listener.set_listen_mode("global"):
            logger.error("❌ 设置全局监听模式失败")
            print("❌ 设置全局监听模式失败")
            return False
        
        print("✅ 全局监听模式设置成功")
        
        # 启动监听
        logger.info("🚀 启动COM全局监听...")
        print("🚀 启动COM全局监听...")
        if not listener.start_listening():
            logger.error("❌ 启动监听失败")
            print("❌ 启动监听失败")
            return False
        
        print("✅ COM全局监听已启动")
        print("📊 监听状态信息:")
        print(f"   - 监听模式: {listener.listen_mode}")
        print(f"   - 监听状态: {listener.is_listening}")
        print(f"   - 线程状态: {'运行中' if listener.global_listener_thread and listener.global_listener_thread.is_alive() else '未运行'}")
        
        # 运行监听一段时间
        test_duration = 15
        logger.info(f"⏳ 运行监听 {test_duration} 秒...")
        print(f"\n⏳ 监听将运行 {test_duration} 秒，请发送一些测试消息...")
        print("💡 您可以给自己发消息来测试监听功能")
        
        for i in range(test_duration):
            time.sleep(1)
            if not listener.is_listening:
                logger.warning("⚠️ 监听意外停止")
                print("⚠️ 监听意外停止")
                break
            
            # 每5秒显示一次状态
            if (i + 1) % 5 == 0:
                thread_alive = listener.global_listener_thread and listener.global_listener_thread.is_alive()
                print(f"📊 {i + 1}秒 - 监听状态: {'正常' if listener.is_listening and thread_alive else '异常'}")
        
        # 测试停止监听
        logger.info("🛑 测试停止COM全局监听...")
        print("\n🛑 开始测试停止COM全局监听...")
        
        start_time = time.time()
        success = listener.stop_listening()
        end_time = time.time()
        
        stop_duration = end_time - start_time
        
        if success:
            logger.info(f"✅ COM全局监听停止成功，用时 {stop_duration:.2f} 秒")
            print(f"✅ 测试成功！COM全局监听停止成功，用时 {stop_duration:.2f} 秒")
            
            # 验证线程确实已停止
            time.sleep(2)  # 等待2秒确保线程完全停止
            thread_alive = listener.global_listener_thread and listener.global_listener_thread.is_alive()
            if not thread_alive:
                print("✅ 验证通过：监听线程已完全停止")
                logger.info("✅ 验证通过：监听线程已完全停止")
                return True
            else:
                print("❌ 验证失败：监听线程仍在运行")
                logger.error("❌ 验证失败：监听线程仍在运行")
                return False
        else:
            logger.error(f"❌ COM全局监听停止失败，用时 {stop_duration:.2f} 秒")
            print(f"❌ 测试失败！COM全局监听无法停止")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        print(f"❌ 测试过程出现异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 启动新COM实现测试")
    print("🚀 新COM全局监听实现测试")
    print("🔬 此测试将验证COM线程初始化方案是否能解决停止问题")
    print()
    
    try:
        result = test_new_com_implementation()
        
        print("\n" + "=" * 60)
        print("📊 测试结果:")
        
        if result:
            print("🎉 测试成功！新的COM实现能够正常启动和停止全局监听")
            print("💡 建议：可以将此实现应用到生产环境")
            logger.info("🎉 测试成功：新COM实现工作正常")
        else:
            print("💥 测试失败！新的COM实现仍无法解决停止问题")
            print("💡 建议：需要考虑其他解决方案")
            logger.error("💥 测试失败：新COM实现无效")
            
        print("\n📋 详细日志已保存到: test/test_new_com.log")
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        logger.info("⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        logger.error(f"❌ 测试脚本异常: {e}")

if __name__ == "__main__":
    main()
