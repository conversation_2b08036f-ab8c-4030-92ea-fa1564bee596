#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息过滤测试脚本
用于测试全局监听中的消息过滤逻辑是否正确
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_message_filtering.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class MockMessage:
    """模拟消息对象"""
    def __init__(self, content, msg_type='text', sender=None, attr=None):
        self.content = content
        self.type = msg_type
        self.sender = sender
        self.attr = attr

def test_message_filtering_logic():
    """测试消息过滤逻辑"""
    print("🧪 测试消息过滤逻辑")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "私聊朋友消息",
            "chat_type": "friend",
            "chat_name": "张三",
            "message": MockMessage("你好", "text", "张三", "friend"),
            "expected": True,  # 应该处理
            "description": "正常的私聊朋友消息，应该被处理"
        },
        {
            "name": "群聊消息",
            "chat_type": "group", 
            "chat_name": "2025年注册营养师备考5群",
            "message": MockMessage("序号变化就是把03填起来了，因为总是有同学问03怎么没有", "text", "某用户", "group"),
            "expected": False,  # 不应该处理
            "description": "群聊消息，应该被过滤掉"
        },
        {
            "name": "群聊中的@消息",
            "chat_type": "group",
            "chat_name": "工作群",
            "message": MockMessage("@所有人 明天开会", "text", "老板", "group"),
            "expected": False,  # 不应该处理
            "description": "群聊中的@消息，也应该被过滤掉"
        },
        {
            "name": "机器人自己的消息",
            "chat_type": "friend",
            "chat_name": "李四",
            "message": MockMessage("对不起，我现在还不具备回答这个问题的能力", "text", "李四", "self"),
            "expected": False,  # 不应该处理
            "description": "机器人自己发送的消息，应该被过滤掉"
        },
        {
            "name": "包含机器人特征的消息",
            "chat_type": "friend", 
            "chat_name": "王五",
            "message": MockMessage("😊 很高兴为您服务", "text", "王五", "friend"),
            "expected": False,  # 不应该处理
            "description": "包含机器人特征关键词的消息，应该被过滤掉"
        },
        {
            "name": "语音消息",
            "chat_type": "friend",
            "chat_name": "赵六",
            "message": MockMessage("[语音消息]", "voice", "赵六", "friend"),
            "expected": True,  # 应该处理
            "description": "私聊语音消息，应该被处理"
        }
    ]
    
    processed_count = 0
    filtered_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['name']}")
        print(f"   描述: {case['description']}")
        print(f"   聊天类型: {case['chat_type']}")
        print(f"   聊天名称: {case['chat_name']}")
        print(f"   消息内容: {case['message'].content}")
        print(f"   消息发送者: {case['message'].sender}")
        print(f"   消息属性: {case['message'].attr}")
        
        # 模拟过滤逻辑
        should_process = simulate_message_filtering(
            case['chat_type'],
            case['chat_name'], 
            case['message']
        )
        
        expected = case['expected']
        result = "✅ 通过" if should_process == expected else "❌ 失败"
        
        print(f"   预期结果: {'处理' if expected else '过滤'}")
        print(f"   实际结果: {'处理' if should_process else '过滤'}")
        print(f"   测试结果: {result}")
        
        if should_process:
            processed_count += 1
        else:
            filtered_count += 1
    
    print(f"\n📊 测试统计:")
    print(f"   总测试用例: {len(test_cases)}")
    print(f"   应该处理的消息: {processed_count}")
    print(f"   应该过滤的消息: {filtered_count}")
    
    return True

def simulate_message_filtering(chat_type, chat_name, msg):
    """模拟消息过滤逻辑"""
    content = msg.content
    sender = msg.sender
    
    # 1. 检查是否是机器人自己发送的消息
    if hasattr(msg, 'attr') and msg.attr == 'self':
        logger.debug(f"🤖 跳过自己发送的消息: {content[:30]}...")
        return False
    
    # 2. 检查消息是否包含机器人回复的特征标识
    if content:
        # 检查是否包含多种风格回复格式（说明是机器人生成的）
        style_patterns = ["（专业优雅型）", "（幽默热情型）", "（亲切自然型）", "（温馨关怀型）"]
        if any(pattern in content for pattern in style_patterns):
            logger.debug(f"🔄 跳过包含多风格格式的机器人回复: {content[:30]}...")
            return False
        
        # 检查其他机器人特征
        bot_keywords = ["😊", "🤖", "刘帅哥", "植发", "发际线", "（递纸巾）", "（摸摸头）"]
        if any(keyword in content for keyword in bot_keywords):
            logger.debug(f"🔄 跳过疑似机器人回复的消息: {content[:30]}...")
            return False
    
    # 3. 严格的消息来源过滤：只处理私聊朋友消息，完全忽略群聊
    if chat_type == 'friend':
        # 确认是私聊朋友消息
        logger.info(f"📱 检测到私聊消息 - 聊天: {chat_name}, 发送者: {sender}")
        return True
    elif chat_type == 'group':
        # 明确跳过所有群聊消息
        logger.debug(f"🚫 跳过群聊消息 - 群名: {chat_name}, 发送者: {sender}, 内容: {content[:30]}...")
        return False
    else:
        # 跳过其他类型的消息
        logger.debug(f"🚫 跳过未知类型消息 - 类型: {chat_type}, 聊天: {chat_name}, 发送者: {sender}")
        return False

def test_group_message_scenarios():
    """专门测试群聊消息场景"""
    print("\n🧪 专门测试群聊消息过滤")
    print("=" * 60)
    
    group_scenarios = [
        {
            "chat_name": "2025年注册营养师备考5群",
            "messages": [
                "序号变化就是把03填起来了，因为总是有同学问03怎么没有",
                "@所有人 请注意考试时间",
                "谢谢老师的解答",
                "有人知道报名截止时间吗？"
            ]
        },
        {
            "chat_name": "工作群",
            "messages": [
                "明天的会议改到下午3点",
                "收到",
                "好的，没问题",
                "@张三 你准备一下PPT"
            ]
        },
        {
            "chat_name": "家庭群",
            "messages": [
                "今天天气不错",
                "晚上回家吃饭吗？",
                "妈妈做了你爱吃的菜",
                "好的，我6点到家"
            ]
        }
    ]
    
    total_messages = 0
    filtered_messages = 0
    
    for group in group_scenarios:
        print(f"\n📱 群聊: {group['chat_name']}")
        
        for i, message_content in enumerate(group['messages'], 1):
            total_messages += 1
            mock_msg = MockMessage(message_content, "text", f"用户{i}", "group")
            
            should_process = simulate_message_filtering("group", group['chat_name'], mock_msg)
            
            status = "🔄 处理" if should_process else "🚫 过滤"
            print(f"   消息 {i}: {message_content[:40]}... -> {status}")
            
            if not should_process:
                filtered_messages += 1
    
    print(f"\n📊 群聊消息测试统计:")
    print(f"   总群聊消息: {total_messages}")
    print(f"   被过滤消息: {filtered_messages}")
    print(f"   过滤率: {filtered_messages/total_messages*100:.1f}%")
    
    if filtered_messages == total_messages:
        print("✅ 所有群聊消息都被正确过滤！")
        return True
    else:
        print("❌ 有群聊消息未被过滤，存在问题！")
        return False

def main():
    """主函数"""
    print("🚀 消息过滤逻辑测试")
    print("🔬 此测试将验证全局监听中的消息过滤是否正确")
    print("=" * 80)
    
    results = []
    
    # 测试1: 基本过滤逻辑
    print("\n" + "🔍 测试1: 基本消息过滤逻辑".center(80, "="))
    results.append(("基本过滤逻辑", test_message_filtering_logic()))
    
    # 测试2: 群聊消息专项测试
    print("\n" + "🔍 测试2: 群聊消息过滤专项测试".center(80, "="))
    results.append(("群聊消息过滤", test_group_message_scenarios()))
    
    # 汇总结果
    print("\n" + "📊 测试结果汇总".center(80, "="))
    all_passed = True
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！消息过滤逻辑正确。")
        print("💡 群聊消息将被正确过滤，只处理私聊消息。")
    else:
        print("💥 部分测试失败！消息过滤逻辑需要修复。")
    
    print(f"\n📋 详细日志已保存到: test/test_message_filtering.log")

if __name__ == "__main__":
    main()
