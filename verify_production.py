#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能助手 - 生产环境包验证脚本
用于验证生产环境包的完整性和依赖
"""

import os
import sys
import importlib.util
import zipfile
from pathlib import Path

def verify_production_package(zip_path):
    """验证生产环境包"""
    print(f"🔍 验证生产环境包: {zip_path}")
    
    if not os.path.exists(zip_path):
        print(f"❌ 错误: 找不到包文件 {zip_path}")
        return False
    
    # 检查ZIP文件结构
    try:
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            files = zipf.namelist()
            
            # 必需的核心文件
            required_files = [
                'main.py',
                'ui.py', 
                'smart_reply_handler_v2.py',
                'wechat_listener.py',
                'database_manager.py',
                'coze_llm_client_pat.py',
                'requirements.txt',
                'config_example.json',
                'PRODUCTION_README.md'
            ]
            
            # 技术文档文件
            doc_files = [
                'README.md',
                'WXAUTO_API_DOC.md', 
                'DEPLOY_GUIDE.md'
            ]
            
            missing_files = []
            found_files = []
            
            # 检查核心文件
            print("\n📋 核心文件检查:")
            for required_file in required_files:
                file_found = any(f.endswith(required_file) for f in files)
                if file_found:
                    found_files.append(required_file)
                    print(f"  ✅ {required_file}")
                else:
                    missing_files.append(required_file)
                    print(f"  ❌ {required_file} - 缺失")
            
            # 检查文档文件
            print("\n📚 技术文档检查:")
            for doc_file in doc_files:
                file_found = any(f.endswith(doc_file) for f in files)
                if file_found:
                    print(f"  ✅ {doc_file}")
                else:
                    print(f"  ⚠️ {doc_file} - 缺失")
            
            # 检查OAuth子模块
            oauth_files = [f for f in files if 'coze_oauth_python_web' in f]
            print(f"\n🔗 OAuth子模块: {'✅ 包含' if oauth_files else '❌ 缺失'}")
            
            if missing_files:
                print(f"\n❌ 验证失败: 缺少 {len(missing_files)} 个必需文件")
                return False
            else:
                print(f"\n✅ 文件结构验证通过: 包含 {len(found_files)} 个核心文件")
                
        return True
        
    except Exception as e:
        print(f"❌ ZIP文件验证失败: {e}")
        return False

def check_dependencies():
    """检查当前环境依赖"""
    print("\n🔧 依赖环境检查:")
    
    # Python版本检查
    python_version = sys.version_info
    if python_version >= (3, 7):
        print(f"  ✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"  ❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro} (需要3.7+)")
        return False
    
    # 关键依赖检查
    required_packages = [
        ('tkinter', 'GUI界面库'),
        ('sqlite3', '数据库支持'),
        ('json', 'JSON处理'),
        ('threading', '多线程支持'),
        ('requests', 'HTTP请求库'),
        ('time', '时间处理'),
        ('datetime', '日期时间'),
        ('logging', '日志系统')
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'sqlite3':
                import sqlite3  
            elif package == 'requests':
                import requests
            else:
                __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} - {description} (缺失)")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing_packages)}")
        print("   请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖检查通过")
        return True

def create_deployment_checklist():
    """创建部署检查清单"""
    checklist = """
# 微信智能助手 - 生产环境部署检查清单

## 📋 部署前检查

### 1. 环境准备
- [ ] Python 3.7+ 已安装
- [ ] 必要依赖已安装 (pip install -r requirements.txt)
- [ ] 微信PC版已安装并登录

### 2. 配置文件设置
- [ ] 将 config_example.json 重命名为 config.json
- [ ] 配置扣子API密钥 (coze_pat_token)
- [ ] 配置Bot ID (coze_bot_id)
- [ ] 配置通义千问API (如需使用)

### 3. 功能验证
- [ ] 运行 python main.py 启动程序
- [ ] 界面正常显示
- [ ] 微信连接成功
- [ ] 添加监控用户测试
- [ ] 发送测试消息验证智能回复

### 4. 生产环境优化
- [ ] 检查日志级别设置
- [ ] 配置自动启动 (如需要)
- [ ] 设置监控和告警 (如需要)

## 🎯 核心功能特性
- ✅ 智能消息合并 (8秒收集期)
- ✅ 消息更正识别
- ✅ 指定用户/全局监听模式  
- ✅ 多LLM支持 (扣子/通义千问)
- ✅ 数据库存储聊天记录
- ✅ 现代化UI界面

## 📞 技术支持
如遇问题请参考:
- WXAUTO_API_DOC.md - 完整技术文档
- README.md - 项目说明
- DEPLOY_GUIDE.md - 部署指南
"""
    
    with open("DEPLOYMENT_CHECKLIST.md", 'w', encoding='utf-8') as f:
        f.write(checklist)
    
    print("✅ 已生成部署检查清单: DEPLOYMENT_CHECKLIST.md")

def main():
    """主函数"""
    print("🎯 微信智能助手 - 生产环境验证工具")
    print("=" * 50)
    
    # 查找最新的生产环境包
    production_zips = [f for f in os.listdir('.') if f.startswith('wx_smart_assistant_production_') and f.endswith('.zip')]
    
    if not production_zips:
        print("❌ 未找到生产环境包文件")
        print("   请先运行 build_production.py 创建生产包")
        return
    
    # 使用最新的包
    latest_zip = sorted(production_zips)[-1]
    
    # 验证包文件
    package_valid = verify_production_package(latest_zip)
    
    # 检查依赖
    deps_valid = check_dependencies()
    
    # 创建部署清单
    create_deployment_checklist()
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 验证结果总结:")
    print(f"  📦 生产包验证: {'✅ 通过' if package_valid else '❌ 失败'}")
    print(f"  🔧 依赖检查: {'✅ 通过' if deps_valid else '❌ 失败'}")
    
    if package_valid and deps_valid:
        print(f"\n🎉 验证通过！可以部署 {latest_zip}")
        print("📋 请参考 DEPLOYMENT_CHECKLIST.md 进行部署")
    else:
        print(f"\n⚠️ 验证未完全通过，请检查上述问题后重试")

if __name__ == "__main__":
    main() 