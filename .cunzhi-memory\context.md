# 项目上下文信息

- 用户反馈全局监控模式下存在问题：1. 发送消息的方法不对 2. 分辨用户的方法不对 3. 误识别用户。需要根据wxauto官方文档修复这些问题。
- 用户选择方案A：全面修复全局监控下的用户识别和消息发送逻辑问题。主要问题包括：1. 用户识别逻辑不准确，可能将群聊名称误识别为用户名；2. 消息发送方法在全局监控下不稳定；3. 消息过滤逻辑中attr检查错误。需要按照wxauto官方文档的最佳实践进行修复。
- 用户反馈三个问题：1. UI界面停止监控后全局监控线程依然继续运行；2. main.py启动时需要注释掉LLM后台检测部分；3. 需要梳理LLM后台调用逻辑包括间隔、发送内容、本地提示词等。选择按顺序逐个解决。
- 用户想了解当前模式下发送给Coze LLM的上下文内容，感觉不太对，需要分析上下文构成并写测试脚本验证。选择先查看代码分析LLM上下文构成，再写测试脚本。
- 用户反馈全局监听三个关键问题：1. 用户识别不准确，对群聊内容进行处理但只需要回复朋友信息；2. 用户已经回复的内容还进行重复回复（如王雪梅）；3. 全局模式无法停止监控，UI显示停止但监听依然继续。需要分析消息日志找出问题根源。
- 用户选择方案A全面修复三个问题：1. 修改用户识别逻辑，只处理私聊消息，忽略群聊；2. 修复重复回复问题，为每个用户生成唯一user_id；3. 改进全局监控停止机制，确保线程能正常响应停止信号。
- 用户发现config.py中对user_id进行了硬编码（"user_id": "7765085253"），这是导致重复回复问题的根本原因。需要将硬编码的user_id改为动态生成或移除。
- 用户需要测试全局监听停止方案：方案1是切换监听模式后停止，方案2是直接程序终结。当前全局监听无法正常停止的问题需要通过测试脚本验证解决方案
