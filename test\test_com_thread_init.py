#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：COM 线程初始化方案验证
用法：在全局监听运行时执行此脚本，测试 COM 线程初始化是否能解决停止问题
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    import pythoncom
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    print("⚠️ 警告：pythoncom 不可用，无法测试 COM 方案")

from wxauto import WeChat
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_com_init.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class COMTestListener:
    """COM 线程初始化测试监听器"""
    
    def __init__(self, wx_instance):
        self.wx = wx_instance
        self.is_listening = False
        self.listener_thread = None
        self.test_message_count = 0
        
    def start_com_listening(self):
        """启动带 COM 初始化的监听"""
        if self.is_listening:
            logger.warning("⚠️ 监听已在运行")
            return False
            
        self.is_listening = True
        self.listener_thread = threading.Thread(target=self._com_listener_thread, daemon=True)
        self.listener_thread.start()
        logger.info("🚀 COM 监听线程已启动")
        return True
        
    def stop_com_listening(self):
        """停止 COM 监听"""
        if not self.is_listening:
            logger.warning("⚠️ 监听未在运行")
            return True
            
        logger.info("🛑 开始停止 COM 监听...")
        self.is_listening = False
        
        # 调用微信的停止方法
        try:
            self.wx.StopListening()
            logger.info("✅ 已调用 wx.StopListening()")
        except Exception as e:
            logger.warning(f"⚠️ 调用 StopListening() 失败: {e}")
        
        # 等待线程结束
        if self.listener_thread and self.listener_thread.is_alive():
            logger.info("⏳ 等待 COM 监听线程结束...")
            
            for attempt in range(10):  # 最多等待10秒
                self.listener_thread.join(timeout=1)
                if not self.listener_thread.is_alive():
                    logger.info(f"✅ COM 监听线程已停止 (用时 {attempt + 1} 秒)")
                    return True
                else:
                    logger.info(f"⏳ 第 {attempt + 1} 秒：线程仍在运行...")
            
            if self.listener_thread.is_alive():
                logger.error("❌ COM 监听线程未能在10秒内停止")
                return False
        
        logger.info("✅ COM 监听已成功停止")
        return True
        
    def _com_listener_thread(self):
        """带 COM 初始化的监听线程"""
        logger.info("🧵 COM 监听线程开始运行")
        
        # COM 初始化
        if COM_AVAILABLE:
            try:
                pythoncom.CoInitialize()
                logger.info("✅ COM 环境初始化成功")
            except Exception as init_error:
                logger.error(f"❌ COM 初始化失败: {init_error}")
                return
        else:
            logger.warning("⚠️ pythoncom 不可用，跳过 COM 初始化")
        
        try:
            # 启动微信监听
            try:
                self.wx.StartListening()
                logger.info("✅ 微信监听已启动")
            except Exception as start_error:
                logger.warning(f"⚠️ 启动微信监听失败: {start_error}")
            
            # 主监听循环
            logger.info("🔄 开始 COM 监听循环...")
            while self.is_listening:
                try:
                    # 检查停止标志
                    if not self.is_listening:
                        logger.info("🛑 检测到停止信号，退出监听循环")
                        break
                    
                    # 检查微信实例
                    if not self.wx:
                        logger.warning("⚠️ 微信实例无效，退出监听循环")
                        break
                    
                    # 获取新消息
                    logger.debug("🔍 等待新消息...")
                    message_data = self.wx.GetNextNewMessage()
                    
                    if message_data:
                        self.test_message_count += 1
                        logger.info(f"📨 收到第 {self.test_message_count} 条测试消息")
                        logger.info(f"📄 消息内容: {message_data}")
                    
                    # 再次检查停止标志
                    if not self.is_listening:
                        logger.info("🛑 消息处理后检测到停止信号")
                        break
                        
                except Exception as msg_error:
                    logger.error(f"❌ 获取消息失败: {msg_error}")
                    
                    # 检查是否是停止相关的异常
                    error_msg = str(msg_error).lower()
                    if any(keyword in error_msg for keyword in ['stop', 'close', 'disconnect', 'invalid']):
                        logger.info("🛑 检测到停止相关异常，正常退出")
                        break
                    
                    # 出错时检查停止标志
                    if not self.is_listening:
                        logger.info("🛑 异常处理中检测到停止信号")
                        break
                    
                    # 短暂休眠，分段检查停止标志
                    for _ in range(10):
                        if not self.is_listening:
                            logger.info("🛑 休眠期间检测到停止信号")
                            break
                        time.sleep(0.1)
                        
        except Exception as thread_error:
            logger.error(f"❌ COM 监听线程异常: {thread_error}")
        finally:
            # COM 清理
            if COM_AVAILABLE:
                try:
                    pythoncom.CoUninitialize()
                    logger.info("✅ COM 环境已清理")
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ COM 清理失败: {cleanup_error}")
            
            logger.info("🏁 COM 监听线程结束")

def test_com_solution():
    """测试 COM 方案"""
    try:
        logger.info("🧪 开始测试 COM 线程初始化方案")
        
        if not COM_AVAILABLE:
            logger.error("❌ pythoncom 不可用，无法进行测试")
            return False
        
        # 初始化微信实例
        logger.info("📱 初始化微信实例...")
        wx = WeChat()
        
        # 创建 COM 测试监听器
        logger.info("🎧 创建 COM 测试监听器...")
        com_listener = COMTestListener(wx)
        
        # 启动 COM 监听
        logger.info("🚀 启动 COM 监听...")
        if not com_listener.start_com_listening():
            logger.error("❌ 启动 COM 监听失败")
            return False
        
        # 等待一段时间让监听稳定运行
        logger.info("⏳ 等待10秒让监听稳定运行...")
        print("📊 COM 监听已启动，等待10秒...")
        print("💡 在这期间您可以发送一些测试消息")
        time.sleep(10)
        
        # 尝试停止监听
        logger.info("🛑 测试停止 COM 监听...")
        print("🛑 开始测试停止 COM 监听...")
        
        start_time = time.time()
        success = com_listener.stop_com_listening()
        end_time = time.time()
        
        stop_duration = end_time - start_time
        
        if success:
            logger.info(f"✅ COM 监听停止成功，用时 {stop_duration:.2f} 秒")
            print(f"✅ 测试成功！COM 方案能够停止监听，用时 {stop_duration:.2f} 秒")
            print(f"📊 测试期间收到 {com_listener.test_message_count} 条消息")
            return True
        else:
            logger.error(f"❌ COM 监听停止失败，用时 {stop_duration:.2f} 秒")
            print(f"❌ 测试失败！COM 方案无法停止监听")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程异常: {e}")
        print(f"❌ 测试过程出现异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 启动 COM 线程初始化方案测试")
    print("🧪 COM 线程初始化方案测试")
    print("=" * 50)
    
    if not COM_AVAILABLE:
        print("❌ 错误：pythoncom 模块不可用")
        print("💡 请确保安装了 pywin32：pip install pywin32")
        return
    
    try:
        result = test_com_solution()
        
        print("\n" + "=" * 50)
        if result:
            print("🎉 测试结果：COM 方案 成功！")
            print("💡 建议：将此方案应用到主程序中")
        else:
            print("💥 测试结果：COM 方案 失败！")
            print("💡 建议：考虑其他解决方案")
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        logger.info("⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        logger.error(f"❌ 测试脚本异常: {e}")

if __name__ == "__main__":
    main()
