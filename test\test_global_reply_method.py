#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局监听回复方法测试脚本
用于测试全局监听模式下是否正确使用msg.reply()方法而不是ChatWith
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_global_reply_method.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class MockMessage:
    """模拟消息对象"""
    def __init__(self, content, sender, attr='friend'):
        self.content = content
        self.sender = sender
        self.attr = attr
        self.reply_called = False
        self.reply_content = None
    
    def reply(self, message):
        """模拟reply方法"""
        self.reply_called = True
        self.reply_content = message
        logger.info(f"📤 MockMessage.reply() 被调用: {message}")
        return {"status": "success"}

class MockListener:
    """模拟监听器"""
    def __init__(self, mode="global"):
        self.listen_mode = mode
        self.wx = None
    
    def get_listen_mode(self):
        return self.listen_mode

class MockWeChatManager:
    """模拟微信管理器"""
    def __init__(self):
        self.sendmsg_called = False
        self.sendmsg_params = {}
    
    def SendMsg(self, msg, who):
        """模拟SendMsg方法"""
        self.sendmsg_called = True
        self.sendmsg_params = {"msg": msg, "who": who}
        logger.info(f"📤 MockWeChatManager.SendMsg() 被调用: msg={msg}, who={who}")
        return {"status": "成功"}

def test_global_reply_method():
    """测试全局监听模式下的回复方法"""
    print("🧪 测试全局监听回复方法")
    print("=" * 60)
    
    # 导入消息发送器
    from core.msg_proc.message_sender import WeChatMessageSender
    
    # 测试用例
    test_cases = [
        {
            "name": "全局模式 - 有msg对象",
            "listen_mode": "global",
            "has_msg_obj": True,
            "expected_method": "msg.reply()",
            "description": "全局监听模式下，有msg对象时应该使用msg.reply()方法"
        },
        {
            "name": "全局模式 - 无msg对象",
            "listen_mode": "global", 
            "has_msg_obj": False,
            "expected_method": "SendMsg()",
            "description": "全局监听模式下，无msg对象时回退到SendMsg()方法"
        },
        {
            "name": "指定模式 - 有msg对象",
            "listen_mode": "specific",
            "has_msg_obj": True,
            "expected_method": "SendMsg()",
            "description": "指定用户监听模式下，始终使用SendMsg()方法"
        },
        {
            "name": "指定模式 - 无msg对象",
            "listen_mode": "specific",
            "has_msg_obj": False,
            "expected_method": "SendMsg()",
            "description": "指定用户监听模式下，始终使用SendMsg()方法"
        }
    ]
    
    results = []
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['name']}")
        print(f"   描述: {case['description']}")
        print(f"   监听模式: {case['listen_mode']}")
        print(f"   有msg对象: {case['has_msg_obj']}")
        print(f"   预期方法: {case['expected_method']}")
        
        # 创建模拟对象
        listener = MockListener(case['listen_mode'])
        wechat_manager = MockWeChatManager()
        msg_obj = MockMessage("测试消息", "测试用户") if case['has_msg_obj'] else None
        
        # 调用发送消息方法
        try:
            success = WeChatMessageSender.send_message(
                wechat_manager=wechat_manager,
                listener=listener,
                to_user="测试用户",
                message="测试回复消息",
                msg_obj=msg_obj
            )
            
            # 检查结果
            if case['expected_method'] == "msg.reply()":
                if msg_obj and msg_obj.reply_called:
                    result = "✅ 通过"
                    print(f"   实际结果: 使用了msg.reply()方法")
                    print(f"   回复内容: {msg_obj.reply_content}")
                else:
                    result = "❌ 失败"
                    print(f"   实际结果: 未使用msg.reply()方法")
            elif case['expected_method'] == "SendMsg()":
                if wechat_manager.sendmsg_called:
                    result = "✅ 通过"
                    print(f"   实际结果: 使用了SendMsg()方法")
                    print(f"   发送参数: {wechat_manager.sendmsg_params}")
                else:
                    result = "❌ 失败"
                    print(f"   实际结果: 未使用SendMsg()方法")
            else:
                result = "❌ 未知"
                print(f"   实际结果: 未知预期方法")
            
            print(f"   发送成功: {success}")
            print(f"   测试结果: {result}")
            
            results.append((case['name'], result == "✅ 通过"))
            
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append((case['name'], False))
    
    return results

def test_message_object_integration():
    """测试消息对象集成"""
    print("\n🧪 测试消息对象集成")
    print("=" * 60)
    
    # 模拟全局监听接收到的消息对象
    mock_messages = [
        {
            "content": "你好",
            "sender": "张三",
            "attr": "friend",
            "chat_type": "friend"
        },
        {
            "content": "今天天气怎么样？",
            "sender": "李四", 
            "attr": "friend",
            "chat_type": "friend"
        }
    ]
    
    print("📱 模拟全局监听接收消息并回复:")
    
    for i, msg_data in enumerate(mock_messages, 1):
        print(f"\n📨 消息 {i}:")
        print(f"   发送者: {msg_data['sender']}")
        print(f"   内容: {msg_data['content']}")
        print(f"   属性: {msg_data['attr']}")
        print(f"   聊天类型: {msg_data['chat_type']}")
        
        # 创建消息对象
        msg_obj = MockMessage(msg_data['content'], msg_data['sender'], msg_data['attr'])
        
        # 模拟回复过程
        if msg_data['chat_type'] == 'friend':
            print(f"   ✅ 识别为私聊消息，准备回复")
            
            # 模拟使用msg.reply()回复
            try:
                reply_content = f"收到您的消息：{msg_data['content']}"
                result = msg_obj.reply(reply_content)
                
                if msg_obj.reply_called:
                    print(f"   📤 使用msg.reply()发送回复: {reply_content}")
                    print(f"   ✅ 回复发送成功")
                else:
                    print(f"   ❌ msg.reply()未被调用")
                    
            except Exception as e:
                print(f"   ❌ 回复发送失败: {e}")
        else:
            print(f"   🚫 非私聊消息，跳过回复")
    
    return True

def main():
    """主函数"""
    print("🚀 全局监听回复方法测试")
    print("🔬 此测试将验证全局监听是否正确使用msg.reply()而不是ChatWith")
    print("=" * 80)
    
    all_results = []
    
    # 测试1: 回复方法选择
    print("\n" + "🔍 测试1: 回复方法选择逻辑".center(80, "="))
    method_results = test_global_reply_method()
    all_results.extend(method_results)
    
    # 测试2: 消息对象集成
    print("\n" + "🔍 测试2: 消息对象集成测试".center(80, "="))
    integration_result = test_message_object_integration()
    all_results.append(("消息对象集成", integration_result))
    
    # 汇总结果
    print("\n" + "📊 测试结果汇总".center(80, "="))
    passed_count = 0
    total_count = len(all_results)
    
    for test_name, result in all_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:30} {status}")
        if result:
            passed_count += 1
    
    print("\n" + "=" * 80)
    print(f"📊 测试统计: {passed_count}/{total_count} 通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！全局监听回复方法正确。")
        print("💡 全局监听将使用msg.reply()方法，避免ChatWith破坏监听连续性。")
    else:
        print("💥 部分测试失败！回复方法逻辑需要修复。")
    
    print(f"\n📋 详细日志已保存到: test/test_global_reply_method.log")

if __name__ == "__main__":
    main()
