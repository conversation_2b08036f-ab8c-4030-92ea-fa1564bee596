# -*- coding: utf-8 -*-
"""
通用工具函数模块
根据项目说明书要求，提供通用的辅助函数
"""

import re
import logging
import time
import hashlib
from datetime import datetime
from functools import wraps
import queue
import logging.handlers
from logging.handlers import Queue<PERSON>and<PERSON>, QueueListener
import os

class QueueLogHandler(logging.Handler):
    """
    将日志记录放入队列中，以便在另一个线程中处理
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

def setup_logging(log_queue=None):
    """
    配置日志系统，支持多线程安全
    :param log_queue: 如果提供，则日志消息将被放入此队列
    :return: 日志队列
    """
    if log_queue is None:
        log_queue = queue.Queue(-1)

    # 基础配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 文件处理器
    log_file = f"wxauto_logs/app_{datetime.now().strftime('%Y%m%d')}.log"
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_file, when='midnight', interval=1, backupCount=7, encoding='utf-8'
    )
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 队列监听器
    listener = QueueListener(log_queue, file_handler, console_handler, respect_handler_level=True)
    listener.start()
    
    # 将日志发送到队列
    queue_handler = QueueHandler(log_queue)
    root_logger.addHandler(queue_handler)

    return log_queue


def retry_on_exception(max_retries=3, delay=1, backoff=2, exceptions=(Exception,)):
    """
    装饰器：为函数添加重试机制
    
    :param max_retries: 最大重试次数
    :param delay: 初始延迟时间（秒）
    :param backoff: 延迟时间倍数
    :param exceptions: 需要重试的异常类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(func.__module__)
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(
                            f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，"
                            f"{wait_time:.1f}秒后重试..."
                        )
                        time.sleep(wait_time)
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
            
            raise last_exception
        return wrapper
    return decorator

def parse_user_choice(message_content, num_options):
    """
    解析用户回复，提取数字选择
    
    :param message_content: 用户消息内容
    :param num_options: 可选项数量
    :return: 用户选择的索引（1-based），如果无法解析则返回None
    """
    logger = logging.getLogger("Utils")
    
    if not message_content or not isinstance(message_content, str):
        return None
    
    # 清理消息内容
    content = message_content.strip()
    
    # 尝试多种模式匹配
    patterns = [
        r'^\s*(\d+)\s*[\.。]?\s*$',  # 纯数字，可能带点号
        r'^\s*选择?\s*(\d+)\s*$',    # "选1" 或 "选择1"
        r'^\s*第\s*(\d+)\s*个?\s*$', # "第1个"
        r'^\s*(\d+)\s*号\s*$',       # "1号"
        r'^\s*[选择]?[第]?(\d+)[个号项]?\s*$'  # 综合模式
    ]
    
    for pattern in patterns:
        match = re.match(pattern, content, re.IGNORECASE)
        if match:
            try:
                choice = int(match.group(1))
                if 1 <= choice <= num_options:
                    logger.debug(f"成功解析用户选择: {choice}")
                    return choice
                else:
                    logger.warning(f"用户选择的数字 {choice} 超出范围 (1-{num_options})")
                    return None
            except ValueError:
                continue
    
    logger.debug(f"无法从消息 '{content}' 解析出有效数字选择")
    return None

def generate_message_id(content, sender, timestamp=None):
    """
    生成消息唯一ID
    
    :param content: 消息内容
    :param sender: 发送者
    :param timestamp: 时间戳，如果为None则使用当前时间
    :return: 消息ID
    """
    if timestamp is None:
        timestamp = datetime.now().isoformat()
    
    # 组合字符串并生成MD5哈希
    combined = f"{sender}:{content}:{timestamp}"
    return hashlib.md5(combined.encode('utf-8')).hexdigest()[:16]

def format_timestamp(dt=None, format_str="%Y-%m-%d %H:%M:%S"):
    """
    格式化时间戳
    
    :param dt: datetime对象，如果为None则使用当前时间
    :param format_str: 格式字符串
    :return: 格式化的时间字符串
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)

def truncate_text(text, max_length=100, suffix="..."):
    """
    截断文本到指定长度
    
    :param text: 原始文本
    :param max_length: 最大长度
    :param suffix: 截断后添加的后缀
    :return: 截断后的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def sanitize_filename(filename):
    """
    清理文件名，移除不合法字符
    
    :param filename: 原始文件名
    :return: 清理后的文件名
    """
    # 移除Windows文件名中的非法字符
    illegal_chars = r'[<>:"/\\|?*]'
    return re.sub(illegal_chars, '_', filename)

def validate_config_value(value, value_type, default=None, min_val=None, max_val=None):
    """
    验证配置值
    
    :param value: 待验证的值
    :param value_type: 期望的类型
    :param default: 默认值
    :param min_val: 最小值（适用于数字）
    :param max_val: 最大值（适用于数字）
    :return: 验证后的值
    """
    try:
        if value is None:
            return default
        
        # 类型转换
        if value_type == int:
            validated_value = int(value)
        elif value_type == float:
            validated_value = float(value)
        elif value_type == str:
            validated_value = str(value)
        elif value_type == bool:
            if isinstance(value, str):
                validated_value = value.lower() in ('true', '1', 'yes', 'on')
            else:
                validated_value = bool(value)
        else:
            validated_value = value
        
        # 范围检查
        if value_type in (int, float) and validated_value is not None:
            if min_val is not None and validated_value < min_val:
                return min_val
            if max_val is not None and validated_value > max_val:
                return max_val
        
        return validated_value
    
    except (ValueError, TypeError):
        return default

def safe_json_loads(json_str, default=None):
    """
    安全的JSON解析
    
    :param json_str: JSON字符串
    :param default: 解析失败时的默认值
    :return: 解析结果或默认值
    """
    try:
        import json
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default

def get_logger(name, level=logging.INFO):
    """
    获取配置好的日志记录器
    
    :param name: 日志记录器名称
    :param level: 日志级别
    :return: 日志记录器
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        logger.setLevel(level)
    
    return logger 