#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能助手 - 生产环境打包脚本
清理开发调试文件，保留核心功能和技术文档
"""

import os
import shutil
import zipfile
from datetime import datetime
import tempfile

def create_production_package():
    """创建生产环境包"""
    
    # 生成打包时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"wx_smart_assistant_production_{timestamp}"
    
    print(f"🚀 开始创建生产环境包: {package_name}")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        production_dir = os.path.join(temp_dir, package_name)
        os.makedirs(production_dir)
        
        # 核心运行文件 - 必需
        core_files = [
            "main.py",
            "ui.py", 
            "smart_reply_handler_v2.py",
            "wechat_listener.py",
            "database_manager.py",
            "coze_llm_client_pat.py",
            "llm_client_qianwen.py",
            "llm_client_unified.py",
            "config.py",
            "utils.py",
            "requirements.txt",
            "start_production.bat"
        ]
        
        # 技术文档和说明 - 保留用于二次开发
        documentation_files = [
            "README.md",
            "DEPLOY_GUIDE.md", 
            "WXAUTO_API_DOC.md",
            "knowledge.txt",
            ".gitignore"
        ]
        
        # 配置文件 - 需要但可能需要用户自定义
        config_files = [
            "config.json"  # 保留作为示例配置
        ]
        
        # 复制核心文件
        print("📁 复制核心运行文件...")
        for file in core_files:
            if os.path.exists(file):
                shutil.copy2(file, production_dir)
                print(f"  ✅ {file}")
            else:
                print(f"  ⚠️ 未找到: {file}")
        
        # 复制文档文件
        print("📚 复制技术文档...")
        for file in documentation_files:
            if os.path.exists(file):
                shutil.copy2(file, production_dir)
                print(f"  ✅ {file}")
            else:
                print(f"  ⚠️ 未找到: {file}")
        
        # 复制配置文件并重命名为示例
        print("⚙️ 复制配置文件...")
        for file in config_files:
            if os.path.exists(file):
                # 复制为示例配置
                example_name = file.replace('.json', '_example.json')
                shutil.copy2(file, os.path.join(production_dir, example_name))
                print(f"  ✅ {file} -> {example_name}")
        
        # 创建生产环境说明
        create_production_readme(production_dir)
        
        # 打包为ZIP文件
        zip_filename = f"{package_name}.zip"
        print(f"📦 打包为ZIP文件: {zip_filename}")
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(production_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, temp_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✅ 生产环境包创建完成: {zip_filename}")
        
        # 显示包内容统计
        show_package_stats(zip_filename)
        
        return zip_filename

def create_production_readme(production_dir):
    """创建生产环境部署说明"""
    readme_content = f"""# 微信智能助手 - 生产环境版本

## 📦 包内容说明

### 🚀 核心运行文件
- `main.py` - 程序入口
- `ui.py` - 用户界面
- `smart_reply_handler_v2.py` - 智能回复处理器
- `wechat_listener.py` - 微信消息监听器
- `database_manager.py` - 数据库管理
- `*.py` - 其他核心模块

### 📚 技术文档 (二次开发必读)
- `README.md` - 项目总体说明
- `WXAUTO_API_DOC.md` - 技术验证文档，API使用说明
- `DEPLOY_GUIDE.md` - 部署指南
- `knowledge.txt` - 知识库文件

### ⚙️ 配置文件
- `config_example.json` - 配置文件示例，使用前重命名为config.json
- `requirements.txt` - Python依赖包列表
- `start_production.bat` - Windows启动脚本

## 🚀 快速部署

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 配置文件设置
copy config_example.json config.json
# 编辑config.json，填入你的API密钥等配置
```

### 2. 启动程序
```bash
# Windows
start_production.bat

# 或直接运行Python
python main.py
```

### 3. 功能特性
- ✅ 智能消息合并 (8秒收集期)
- ✅ 消息更正识别
- ✅ 指定用户/全局监听模式
- ✅ 多LLM支持 (扣子/通义千问)
- ✅ 数据库存储聊天记录
- ✅ 现代化UI界面

## 📖 二次开发指南

详细的技术文档请参考:
- `WXAUTO_API_DOC.md` - 包含完整的API验证和使用说明
- `README.md` - 架构设计和开发规范

## 📝 版本信息
- 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- 版本: 生产环境优化版本
- 特性: 智能消息处理 + 8秒合并优化 + UI改进

## ⚠️ 注意事项
1. 首次使用需要配置config.json文件
2. 确保微信客户端已登录
3. 建议在Windows环境下运行

## 🛠️ 技术支持
如遇问题，请参考技术文档或检查日志文件进行排查。
"""
    
    readme_path = os.path.join(production_dir, "PRODUCTION_README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"  ✅ PRODUCTION_README.md")

def show_package_stats(zip_filename):
    """显示打包统计信息"""
    with zipfile.ZipFile(zip_filename, 'r') as zipf:
        files = zipf.infolist()
        total_size = sum(f.file_size for f in files)
        compressed_size = sum(f.compress_size for f in files)
        
        print(f"\n📊 打包统计:")
        print(f"  📁 文件数量: {len(files)}")
        print(f"  📏 原始大小: {total_size / 1024 / 1024:.1f} MB")
        print(f"  🗜️ 压缩大小: {compressed_size / 1024 / 1024:.1f} MB")
        print(f"  📉 压缩率: {(1 - compressed_size/total_size)*100:.1f}%")
        
        print(f"\n📋 包含的主要文件:")
        for f in files:
            if f.filename.endswith(('.py', '.md', '.txt', '.json', '.bat')):
                print(f"  - {f.filename}")

def clean_current_development_files():
    """清理当前目录的开发调试文件"""
    print("\n🧹 清理开发调试文件...")
    
    # 可以安全删除的文件和目录
    files_to_clean = [
        "__pycache__",
        "*.pyc", 
        "*.pyo",
        "*.log",
        "test_*.py",
        "test_*.log", 
        "debug_*.log",
        "temp_*",
        "*.bak",
        "wx_smart_assistant_deploy_*.zip",  # 旧的部署包
        "wxauto_logs"  # 日志目录可重新生成
    ]
    
    import glob
    cleaned_count = 0
    
    for pattern in files_to_clean:
        matches = glob.glob(pattern)
        for match in matches:
            try:
                if os.path.isdir(match):
                    shutil.rmtree(match)
                    print(f"  🗑️ 删除目录: {match}")
                else:
                    os.remove(match)
                    print(f"  🗑️ 删除文件: {match}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ⚠️ 无法删除 {match}: {e}")
    
    print(f"✅ 清理完成，共清理 {cleaned_count} 个文件/目录")

if __name__ == "__main__":
    print("🎯 微信智能助手 - 生产环境打包工具")
    print("=" * 50)
    
    # 创建生产环境包
    package_file = create_production_package()
    
    # 询问是否清理开发文件
    print(f"\n❓ 是否清理当前目录的开发调试文件？")
    print("   (这将删除__pycache__, *.log, test_*等开发文件)")
    choice = input("   输入 y 确认清理，任意键跳过: ").lower().strip()
    
    if choice == 'y':
        clean_current_development_files()
    else:
        print("⏭️ 跳过清理，保留所有开发文件")
    
    print(f"\n🎉 打包完成！生产环境包: {package_file}")
    print(f"📋 可以将此ZIP文件部署到生产环境中使用") 