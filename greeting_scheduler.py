# -*- coding: utf-8 -*-
"""
每日问候调度器模块
"""

import logging
import time
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from config import Config
from llm_client_unified import LLMClientUnified
from wechat_listener import WeChatListener

class GreetingScheduler:
    def __init__(self, log_queue=None, status_queue=None, command_queue=None):
        self.logger = logging.getLogger("GreetingScheduler")
        self.config = Config()
        self.llm_client = LLMClientUnified(self.config)
        
        # 调度器不直接控制微信，而是通过一个独立的listener实例
        # 注意：这里的listener仅用于发送消息，不用于启动监听循环
        self.wechat_listener = WeChatListener(
            config=self.config,
            log_queue=log_queue,
            status_queue=status_queue,
            command_queue=command_queue
        )
        
        self.scheduler = BackgroundScheduler(timezone="Asia/Shanghai")
        self.job = None

    def start(self):
        """启动调度器"""
        if self.job is None:
            # 每天早上10:30执行
            self.job = self.scheduler.add_job(
                self._send_daily_greeting, 
                'cron', 
                hour=10, 
                minute=30
            )
            self.scheduler.start()
            self.logger.info("✅ 每日问候任务已启动，将于每天10:30发送问候。")
        else:
            self.logger.warning("调度器已经运行中。")

    def stop(self):
        """停止调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown()
            self.job = None
            self.logger.info("每日问候任务已停止。")

    def _send_daily_greeting(self):
        """生成并发送每日问候的核心任务"""
        self.logger.info("🚀 开始执行每日问候任务...")
        
        # 每次执行任务时重新获取最新的微信实例
        wx_instance = self.wechat_listener.get_wechat_instance()
        if not wx_instance:
            self.logger.error("无法获取微信实例，跳过每日问候。")
            return

        monitored_users = self.config.get("wechat", "monitored_users", [])
        if not monitored_users:
            self.logger.warning("没有配置需要监控的用户，跳过每日问候。")
            return

        # 1. 生成问候语
        greeting_message = self._generate_greeting_message()
        if not greeting_message:
            self.logger.error("未能生成问候语，任务中止。")
            return
            
        self.logger.info(f"成功生成问候语: {greeting_message}")

        # 2. 发送问候语
        self.logger.info(f"准备向 {len(monitored_users)} 位用户发送问候: {monitored_users}")
        for user in monitored_users:
            try:
                # 直接使用wx_instance发送消息
                wx_instance.SendMsg(who=user, msg=greeting_message)
                self.logger.info(f"已向 [{user}] 发送问候。")
                time.sleep(1) # 增加一个小的延迟，避免发送过快
            except Exception as e:
                self.logger.error(f"向 [{user}] 发送问候失败: {e}", exc_info=True)
        
        self.logger.info("✅ 每日问候任务执行完毕。")

    def _generate_greeting_message(self) -> str:
        """调用LLM生成问候语"""
        try:
            today = datetime.now().strftime('%Y年%m月%d日')
            prompt = (
                f"今天是{today}。"
                "请为我的朋友创作一条充满正能量、富有哲理的早安问候语，风格要非常温馨、亲切。"
                "如果今天是中国的某个节气、传统节日或重要的纪念日，请巧妙地结合当天特色进行创作。"
                "请使用表情符号（emoji）和换行来美化排版，让问候看起来更精致、更温暖。"
                "最终内容的总字数（不含emoji和换行）请控制在50字以内。"
                "请直接返回最终的问候语，不要包含任何多余的解释或标题。"
            )
            
            messages = [{"role": "user", "content": prompt}]
            
            # 调用LLM生成
            responses = self.llm_client.generate_response(messages, temperature=0.8, num_choices=1)
            
            if responses:
                return responses[0].strip()
            else:
                self.logger.error("LLM未能返回任何问候语内容。")
                return ""
        except Exception as e:
            self.logger.error(f"生成问候语时发生错误: {e}", exc_info=True)
            return "" 