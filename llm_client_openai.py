# -*- coding: utf-8 -*-
"""
OpenAI兼容LLM客户端
适用于所有遵循OpenAI API规范的服务，如SiliconFlow
"""

import logging
from typing import List, Dict, Any
from openai import OpenAI, APIConnectionError, RateLimitError

class OpenAICompatibleLLMClient:
    """适用于OpenAI兼容API的LLM客户端"""
    
    def __init__(self, config):
        self.logger = logging.getLogger("OpenAICompatibleLLMClient")
        self.config = config
        
        self.api_key = self.config.get("llm_providers", "current_provider_config", {}).get("api_key")
        self.base_url = self.config.get("llm_providers", "current_provider_config", {}).get("api_url")
        self.model_name = self.config.get("llm_providers", "current_provider_config", {}).get("model_name")
        
        if not all([self.api_key, self.base_url, self.model_name]):
            raise ValueError("OpenAI兼容客户端的api_key, base_url或model_name未配置")
            
        try:
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            self.logger.info("✅ OpenAI兼容客户端初始化成功")
        except Exception as e:
            self.logger.error(f"初始化OpenAI客户端失败: {e}", exc_info=True)
            raise

    def generate_response(self, messages: List[Dict[str, str]], temperature: float = 0.7, num_choices: int = 1) -> List[str]:
        """生成回复"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=temperature,
                n=num_choices,
            )
            return [choice.message.content for choice in response.choices if choice.message.content]
        except APIConnectionError as e:
            self.logger.error(f"API连接错误: {e}")
        except RateLimitError as e:
            self.logger.error(f"API速率限制错误: {e}")
        except Exception as e:
            self.logger.error(f"生成回复时发生未知错误: {e}", exc_info=True)
        return []

    def generate_choices(self, context_messages: List[Dict[str, str]], current_message: str, num_choices: int = 3) -> List[str]:
        """为当前消息生成多个备选回复"""
        # 对于大部分OpenAI兼容模型，直接使用generate_response即可
        messages = context_messages + [{"role": "user", "content": current_message}]
        return self.generate_response(messages, num_choices=num_choices)

    def test_connection(self) -> tuple[bool, str]:
        """测试与API的连接"""
        try:
            self.client.models.list()
            return True, "连接成功"
        except APIConnectionError as e:
            self.logger.error(f"测试连接失败: API连接错误 - {e}")
            return False, f"API连接错误: {e}"
        except RateLimitError as e:
            self.logger.error(f"测试连接失败: API速率限制 - {e}")
            return False, f"API速率限制: {e}"
        except Exception as e:
            self.logger.error(f"测试连接时发生未知错误: {e}", exc_info=True)
            return False, f"发生未知错误: {e}"

    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        return {
            "provider_name": "OpenAI Compatible",
            "model_name": self.model_name,
            "api_url": self.base_url,
            "status": "已初始化"
        } 