#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本1：切换监听模式后停止全局监听
用法：在全局监听运行时执行此脚本，测试切换模式停止方案
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from wxauto import WeChat
from wechat_listener import WeChatListener
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test/test_method1.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def test_switch_mode_stop():
    """测试切换监听模式后停止的方案"""
    try:
        logger.info("🧪 开始测试方案1：切换监听模式后停止")
        
        # 初始化微信实例
        logger.info("📱 初始化微信实例...")
        wx = WeChat()
        
        # 创建监听器实例
        logger.info("🎧 创建监听器实例...")
        listener = WeChatListener(wx)
        
        # 检查当前是否有全局监听在运行
        if not listener.is_listening or listener.listen_mode != "global":
            logger.error("❌ 当前没有全局监听在运行，请先启动全局监听")
            return False
        
        logger.info("✅ 检测到全局监听正在运行")
        logger.info(f"📊 当前监听模式: {listener.listen_mode}")
        logger.info(f"🔄 监听状态: {listener.is_listening}")
        
        # 方案1：切换到列表监听模式
        logger.info("🔄 步骤1：尝试切换到列表监听模式...")
        
        # 保存原始状态
        original_mode = listener.listen_mode
        original_thread = listener.global_listener_thread
        
        # 设置为列表监听模式
        listener.listen_mode = "specific"
        logger.info("✅ 已设置监听模式为 specific")
        
        # 尝试添加一个虚拟的监听联系人（如果有的话）
        try:
            # 获取最近联系人
            sessions = wx.GetSession()
            if sessions:
                test_contact = sessions[0]  # 使用第一个联系人作为测试
                logger.info(f"📞 添加测试联系人监听: {test_contact}")
                
                # 添加监听
                def dummy_callback(msg, chat):
                    pass
                
                wx.AddListenChat(nickname=test_contact, callback=dummy_callback)
                logger.info("✅ 已添加测试联系人监听")
                
                # 启动列表监听
                wx.StartListening()
                logger.info("✅ 已启动列表监听")
                
        except Exception as add_error:
            logger.warning(f"⚠️ 添加测试监听失败: {add_error}")
        
        # 等待一小段时间让状态稳定
        logger.info("⏳ 等待2秒让状态稳定...")
        time.sleep(2)
        
        # 步骤2：调用停止监听
        logger.info("🛑 步骤2：调用停止监听方法...")
        success = listener.stop_listening()
        
        if success:
            logger.info("✅ stop_listening() 调用成功")
        else:
            logger.error("❌ stop_listening() 调用失败")
        
        # 步骤3：检查全局监听线程状态
        logger.info("🔍 步骤3：检查全局监听线程状态...")
        
        if original_thread and original_thread.is_alive():
            logger.warning("⚠️ 全局监听线程仍在运行，等待5秒...")
            time.sleep(5)
            
            if original_thread.is_alive():
                logger.error("❌ 全局监听线程仍未停止")
                return False
            else:
                logger.info("✅ 全局监听线程已停止")
                return True
        else:
            logger.info("✅ 全局监听线程已停止")
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 启动测试脚本1：切换监听模式停止方案")
    
    try:
        result = test_switch_mode_stop()
        
        if result:
            logger.info("🎉 测试结果：方案1 成功！")
            print("\n✅ 方案1测试成功：切换监听模式后能够停止全局监听")
        else:
            logger.error("💥 测试结果：方案1 失败！")
            print("\n❌ 方案1测试失败：切换监听模式后仍无法停止全局监听")
            
    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断测试")
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试脚本异常: {e}")
        print(f"\n❌ 测试脚本出现异常: {e}")

if __name__ == "__main__":
    main()
