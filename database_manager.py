# -*- coding: utf-8 -*-
"""
数据库管理模块
根据项目说明书要求，实现本地知识库与聊天记录管理，确保数据不丢失
"""

import sqlite3
import json
import logging
import os
import threading
from datetime import datetime, timedelta
from utils import format_timestamp, safe_json_loads

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger("DatabaseManager")
        
        # 数据库配置
        self.db_path = config.get("database", "db_path", "chat_archive.db")
        self.knowledge_path = config.get("database", "knowledge_path", "knowledge.txt")
        
        # 线程安全
        self._lock = threading.Lock()
        
        # 知识库缓存
        self.knowledge_base = ""
        
        # 初始化
        self._init_database()
        self._load_knowledge_base()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 聊天存档表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS chat_archive (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        chat_partner TEXT NOT NULL,
                        direction TEXT NOT NULL CHECK (direction IN ('incoming', 'outgoing')),
                        message_type TEXT NOT NULL DEFAULT 'text',
                        content TEXT NOT NULL,
                        llm_options TEXT,
                        user_selected_index INTEGER,
                        message_id TEXT UNIQUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    );
                """)
                
                # 学习语料表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS learning_corpus (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        chat_partner TEXT NOT NULL,
                        original_question TEXT NOT NULL,
                        llm_all_options TEXT NOT NULL,
                        user_chosen_option TEXT NOT NULL,
                        user_chosen_index INTEGER NOT NULL,
                        context_length INTEGER DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    );
                """)
                
                # 系统状态表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        component TEXT NOT NULL,
                        status TEXT NOT NULL,
                        message TEXT,
                        timestamp TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    );
                """)
                
                # 创建索引以提高查询性能
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_chat_archive_partner_time 
                    ON chat_archive(chat_partner, timestamp DESC);
                """)
                
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_learning_corpus_partner_time 
                    ON learning_corpus(chat_partner, timestamp DESC);
                """)
                
                conn.commit()
                self.logger.info("数据库表结构初始化完成")
                
        except Exception as e:
            self.logger.error(f"初始化数据库失败: {e}", exc_info=True)
            raise
    
    def _load_knowledge_base(self):
        """加载本地知识库"""
        try:
            if os.path.exists(self.knowledge_path):
                with open(self.knowledge_path, "r", encoding="utf-8") as f:
                    self.knowledge_base = f.read().strip()
                    self.logger.info(f"本地知识库加载成功 (长度: {len(self.knowledge_base)} 字符)")
            else:
                self.knowledge_base = ""
                self.logger.warning(f"知识库文件 '{self.knowledge_path}' 不存在，将使用空知识库")
                self._create_default_knowledge_base()
                
        except Exception as e:
            self.logger.error(f"加载知识库时发生错误: {e}", exc_info=True)
            self.knowledge_base = ""
    
    def _create_default_knowledge_base(self):
        """创建默认知识库文件"""
        try:
            default_knowledge = """# 微信助手知识库

## 基本信息
我是一个智能微信助手，可以帮助您进行对话和回复消息。

## 功能特点
- 智能对话回复
- 上下文理解
- 多选项回复生成
- 学习用户偏好

## 使用说明
您可以编辑这个文件来自定义助手的知识库内容。
助手会根据这些信息来生成更准确的回复。
"""
            
            with open(self.knowledge_path, "w", encoding="utf-8") as f:
                f.write(default_knowledge)
            
            self.knowledge_base = default_knowledge
            self.logger.info("已创建默认知识库文件")
            
        except Exception as e:
            self.logger.error(f"创建默认知识库失败: {e}")
    
    def save_chat_message(self, timestamp, chat_partner, direction, content, 
                         message_type="text", llm_options=None, user_selected_index=None, 
                         message_id=None):
        """保存单条聊天消息到数据库"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    if not timestamp:
                        timestamp = format_timestamp()
                    
                    cursor.execute("""
                        INSERT INTO chat_archive 
                        (timestamp, chat_partner, direction, message_type, content, 
                         llm_options, user_selected_index, message_id) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        timestamp, chat_partner, direction, message_type, content,
                        json.dumps(llm_options, ensure_ascii=False) if llm_options else None,
                        user_selected_index, message_id
                    ))
                    
                    conn.commit()
                    self.logger.debug(f"聊天消息已保存: {content[:30]}...")
                    
        except sqlite3.IntegrityError:
            self.logger.warning(f"消息ID {message_id} 已存在，跳过重复保存")
        except Exception as e:
            self.logger.error(f"保存聊天消息失败: {e}", exc_info=True)
    
    def save_learning_corpus(self, timestamp, chat_partner, original_question, 
                           llm_all_options, user_chosen_option, user_chosen_index,
                           context_length=0):
        """保存学习语料到数据库"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    if not timestamp:
                        timestamp = format_timestamp()
                    
                    cursor.execute("""
                        INSERT INTO learning_corpus 
                        (timestamp, chat_partner, original_question, llm_all_options, 
                         user_chosen_option, user_chosen_index, context_length) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        timestamp, chat_partner, original_question,
                        json.dumps(llm_all_options, ensure_ascii=False),
                        user_chosen_option, user_chosen_index, context_length
                    ))
                    
                    conn.commit()
                    self.logger.info(f"学习语料已保存: {original_question[:30]}... -> {user_chosen_option[:30]}...")
                    
        except Exception as e:
            self.logger.error(f"保存学习语料失败: {e}", exc_info=True)
    
    def save_user_choice(self, timestamp, chat_partner, original_question, 
                        llm_all_options, user_chosen_option, user_chosen_index,
                        context_length=0):
        """保存用户选择（save_learning_corpus的别名，用于向后兼容）"""
        return self.save_learning_corpus(
            timestamp, chat_partner, original_question,
            llm_all_options, user_chosen_option, user_chosen_index,
            context_length
        )
    
    def save_system_status(self, component, status, message=""):
        """
        保存系统状态记录
        
        :param component: 组件名称
        :param status: 状态
        :param message: 状态消息
        """
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    timestamp = format_timestamp()
                    
                    cursor.execute("""
                        INSERT INTO system_status (component, status, message, timestamp) 
                        VALUES (?, ?, ?, ?)
                    """, (component, status, message, timestamp))
                    
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {e}", exc_info=True)
    
    def get_chat_history(self, chat_partner, limit=10, message_types=None):
        """获取指定聊天对象的历史记录"""
        history = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                where_clause = "WHERE chat_partner = ?"
                params = [chat_partner]
                
                if message_types:
                    if isinstance(message_types, str):
                        message_types = [message_types]
                    
                    placeholders = ','.join(['?' for _ in message_types])
                    where_clause += f" AND message_type IN ({placeholders})"
                    params.extend(message_types)
                
                query = f"""
                    SELECT direction, content, message_type, timestamp 
                    FROM chat_archive 
                    {where_clause}
                    ORDER BY timestamp DESC 
                    LIMIT ?
                """
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                for row in reversed(rows):
                    direction, content, msg_type, timestamp = row
                    role = "assistant" if direction == "outgoing" else "user"
                    history.append({
                        "role": role,
                        "content": content,
                        "type": msg_type,
                        "timestamp": timestamp
                    })
                
        except Exception as e:
            self.logger.error(f"获取聊天历史记录失败: {e}", exc_info=True)
        
        return history
    
    def get_context_for_llm(self, chat_partner, history_limit=5, include_knowledge=True):
        """整合知识库和历史记录为LLM上下文"""
        context_messages = []
        
        try:
            if include_knowledge and self.knowledge_base:
                system_content = f"你是一个智能微信助手。以下是你的知识库：\n\n{self.knowledge_base}"
                context_messages.append({
                    "role": "system", 
                    "content": system_content
                })
            else:
                context_messages.append({
                    "role": "system", 
                    "content": "你是一个智能微信助手，请友好、专业地回复用户消息。"
                })
            
            history = self.get_chat_history(chat_partner, history_limit, "text")
            context_messages.extend(history)
            
        except Exception as e:
            self.logger.error(f"构建LLM上下文失败: {e}", exc_info=True)
            context_messages = [{
                "role": "system", 
                "content": "你是一个智能微信助手。"
            }]
        
        return context_messages
    
    def get_learning_corpus(self, chat_partner=None, limit=100):
        """
        获取学习语料
        
        :param chat_partner: 特定聊天对象，None表示所有
        :param limit: 返回数量限制
        :return: 学习语料列表
        """
        corpus = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if chat_partner:
                    query = """
                        SELECT original_question, llm_all_options, user_chosen_option, 
                               user_chosen_index, timestamp
                        FROM learning_corpus 
                        WHERE chat_partner = ? 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """
                    cursor.execute(query, (chat_partner, limit))
                else:
                    query = """
                        SELECT original_question, llm_all_options, user_chosen_option, 
                               user_chosen_index, timestamp, chat_partner
                        FROM learning_corpus 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    """
                    cursor.execute(query, (limit,))
                
                rows = cursor.fetchall()
                
                for row in rows:
                    corpus_item = {
                        "question": row[0],
                        "all_options": safe_json_loads(row[1], []),
                        "chosen_option": row[2],
                        "chosen_index": row[3],
                        "timestamp": row[4]
                    }
                    
                    if not chat_partner:  # 包含chat_partner字段
                        corpus_item["chat_partner"] = row[5]
                    
                    corpus.append(corpus_item)
                
        except Exception as e:
            self.logger.error(f"获取学习语料失败: {e}", exc_info=True)
        
        return corpus
    
    def cleanup_old_records(self, days_to_keep=30):
        """
        清理旧记录
        
        :param days_to_keep: 保留天数
        """
        try:
            cutoff_date = datetime.now()
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_to_keep)
            cutoff_str = format_timestamp(cutoff_date)
            
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 清理聊天记录
                    cursor.execute("""
                        DELETE FROM chat_archive 
                        WHERE timestamp < ?
                    """, (cutoff_str,))
                    chat_deleted = cursor.rowcount
                    
                    # 清理系统状态记录
                    cursor.execute("""
                        DELETE FROM system_status 
                        WHERE timestamp < ?
                    """, (cutoff_str,))
                    status_deleted = cursor.rowcount
                    
                    conn.commit()
                    
                    self.logger.info(f"清理完成: 删除了 {chat_deleted} 条聊天记录, {status_deleted} 条状态记录")
                    
        except Exception as e:
            self.logger.error(f"清理旧记录失败: {e}", exc_info=True)
    
    def backup_database(self, backup_path=None):
        """
        备份数据库
        
        :param backup_path: 备份文件路径
        :return: 备份是否成功
        """
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.db_path}.backup_{timestamp}"
            
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)
            
            self.logger.info(f"数据库备份完成: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}", exc_info=True)
            return False
    
    def get_statistics(self):
        """获取数据库统计信息"""
        stats = {}
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM chat_archive")
                stats["total_messages"] = cursor.fetchone()[0]
                
                cursor.execute("""
                    SELECT chat_partner, COUNT(*) 
                    FROM chat_archive 
                    GROUP BY chat_partner 
                    ORDER BY COUNT(*) DESC 
                    LIMIT 10
                """)
                stats["top_partners"] = cursor.fetchall()
                
                cursor.execute("SELECT COUNT(*) FROM learning_corpus")
                stats["learning_samples"] = cursor.fetchone()[0]
                
                stats["knowledge_base_length"] = len(self.knowledge_base)
                
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}", exc_info=True)
        
        return stats
    
    def reload_knowledge_base(self):
        """重新加载知识库"""
        self._load_knowledge_base()
        return self.knowledge_base 