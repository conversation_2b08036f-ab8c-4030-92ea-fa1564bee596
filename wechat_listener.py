#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信监听管理器 - 支持两种模式
1. 指定用户监听模式 (AddListenChat)
2. 全局监听模式 (GetNextNewMessage)
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Callable, Optional

import pythoncom
from wxauto import WeChat
from utils import retry_on_exception

logger = logging.getLogger(__name__)

class WeChatListener:
    """支持两种监听模式的微信监听管理器"""
    
    def __init__(self, on_message_callback: Callable = None, config=None, 
                 log_queue=None, status_queue=None, command_queue=None):
        """
        初始化监听管理器
        
        Args:
            on_message_callback: 消息处理回调函数，签名为 callback(sender_nickname, message_content, message_type)
            config: 配置对象，用于获取微信路径等配置信息
        """
        self.wx = None
        self.on_message_callback = on_message_callback
        self.config = config
        self.log_queue = log_queue
        self.status_queue = status_queue
        self.command_queue = command_queue
        
        self.is_listening = False
        self.is_connected = False
        self.message_counts = {}  # 各用户的消息计数
        self.monitor_users = []   # 监控用户列表
        self.listener_thread = None
        self._lock = threading.Lock()
        
        # 新增：监听模式
        self.listen_mode = "specific"  # "specific" 或 "global"


    def get_wechat_instance(self):
        """获取并返回一个有效的微信实例"""
        if self.wx and self.is_connected:
            return self.wx
        
        # 如果没有缓存的实例，则尝试初始化一个新的
        if self.init_wechat_instance():
            return self.wx
            
        return None

    def init_wechat_instance(self):
        """初始化微信实例，并进行连接测试"""
        try:
            # 初始化COM库（主线程）
            main_com_initialized = False
            try:
                pythoncom.CoInitialize()
                main_com_initialized = True
                logger.info("✅ 主线程COM环境初始化成功")
            except Exception as com_error:
                logger.warning(f"⚠️ 主线程COM初始化失败: {com_error}")

            # 第一步：检查微信进程状态
            logger.info("🔍 正在检查微信进程状态...")
            process_running = self.check_wechat_process()
            
            # 第二步：尝试连接现有的微信实例
            logger.info("🔗 正在尝试连接现有微信实例...")
            try:
                self.wx = WeChat()
                # 测试实例是否可用 - 使用基础方法验证
                if hasattr(self.wx, 'SendMsg') and callable(self.wx.SendMsg):
                    logger.info("✅ 成功连接到现有微信实例")
                    self.is_connected = True
                    return True
                else:
                    logger.warning("⚠️ 微信实例创建成功但功能不可用，可能需要重新启动")
                    self.wx = None
            except Exception as e:
                logger.info(f"📝 连接现有微信实例失败: {e}")
                self.wx = None
            
            # 第三步：如果微信进程不存在且配置了路径，尝试启动微信程序
            if not process_running and self.config and self.config.get("wechat", "wechat_path"):
                wechat_path = self.config.get("wechat", "wechat_path")
                logger.info(f"🚀 微信进程未运行，使用配置路径启动: {wechat_path}")
                
                try:
                    import subprocess
                    import time
                    import os
                    
                    if os.path.exists(wechat_path):
                        # 启动微信程序
                        subprocess.Popen([wechat_path], shell=True)
                        logger.info("⏳ 微信程序已启动，等待登录和初始化...")
                        
                        # 等待微信启动完成
                        max_wait_time = 60  # 最多等待60秒（包含登录时间）
                        wait_interval = 3   # 每3秒检查一次
                        
                        for attempt in range(max_wait_time // wait_interval):
                            time.sleep(wait_interval)
                            try:
                                self.wx = WeChat()
                                # 验证基础功能是否可用
                                if hasattr(self.wx, 'SendMsg') and callable(self.wx.SendMsg):
                                    logger.info("✅ 微信启动成功并连接")
                                    self.is_connected = True
                                    return True
                            except Exception as inner_e:
                                logger.debug(f"第 {attempt + 1} 次连接尝试失败: {inner_e}")
                                continue
                        
                        logger.error("❌ 微信启动超时，可能需要手动登录或检查路径配置")
                    else:
                        logger.error(f"❌ 微信路径不存在: {wechat_path}")
                        
                except Exception as e:
                    logger.error(f"❌ 启动微信程序失败: {e}")
            
            # 第四步：最后尝试直接创建微信实例（用户手动启动的情况）
            if process_running:
                logger.info("🔄 微信进程正在运行，进行连接尝试...")
            else:
                logger.info("❓ 请确保微信已手动启动并登录，然后重试连接...")
                
            try:
                self.wx = WeChat()
                # 验证基础功能是否可用
                if hasattr(self.wx, 'SendMsg') and callable(self.wx.SendMsg):
                    logger.info("✅ 微信实例连接成功")
                    self.is_connected = True
                    return True
                else:
                    raise Exception("微信实例缺少必要的方法")
            except Exception as e:
                logger.error(f"❌ 最终连接尝试失败: {e}")
                logger.error("💡 建议检查:")
                logger.error("   1. 微信是否已正确安装并登录")
                logger.error("   2. 微信版本是否与wxauto兼容")
                logger.error("   3. 配置的微信路径是否正确")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ 微信初始化过程中发生意外错误: {e}")
            return False
        finally:
            # 释放主线程COM库
            if main_com_initialized:
                try:
                    pythoncom.CoUninitialize()
                    logger.info("✅ 主线程COM环境已清理")
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ 主线程COM清理失败: {cleanup_error}")
    
    def set_listen_mode(self, mode: str) -> bool:
        """
        设置监听模式
        
        Args:
            mode: "specific" (指定用户) 或 "global" (全局)
            
        Returns:
            bool: 是否设置成功
        """
        if mode not in ["specific", "global"]:
            logger.error(f"❌ 无效的监听模式: {mode}")
            return False
            
        # 如果正在监听，需要先停止再切换
        was_listening = self.is_listening
        if was_listening:
            logger.info(f"🔄 正在从 {self.listen_mode} 模式切换到 {mode} 模式...")
            self.stop_listening()
            
        self.listen_mode = mode
        logger.info(f"✅ 监听模式已设置为: {mode}")
        
        # 如果之前在监听，重新启动
        if was_listening:
            self.start_listening()
            
        return True
    
    def get_listen_mode(self) -> str:
        """获取当前监听模式"""
        return self.listen_mode
    
    def check_wechat_process(self) -> bool:
        """检查微信进程是否正在运行"""
        try:
            import psutil
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                    logger.info(f"🔍 发现微信进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    return True
                    
            logger.info("📝 未发现微信进程")
            return False
            
        except ImportError:
            logger.warning("⚠️ psutil 模块未安装，无法检查进程状态")
            return True  # 假设微信正在运行
        except Exception as e:
            logger.warning(f"⚠️ 检查微信进程失败: {e}")
            return True  # 假设微信正在运行
    
    def _on_message_received(self, msg, chat, nickname):
        """
        指定用户监听的消息处理回调
        
        Args:
            msg: 消息对象
            chat: 聊天对象/发送者
            nickname: 用户昵称
        """
        try:
            # 避免处理自己发送的消息（检查是否是机器人自己发的）
            if hasattr(msg, 'sender') and msg.sender in ['base', 'self']:
                logger.info(f"跳过自己发送的消息 (发送者: {msg.sender})")
                return
            
            # 确保消息来自监控的用户
            if msg.sender == nickname:
                logger.info(f"📨 [指定模式] 收到来自监控用户 [{nickname}] 的消息")
            else:
                logger.debug(f"跳过非监控用户的消息 (发送者: {msg.sender}, 监控用户: {nickname})")
                return

            sender_obj_str = str(chat)
            msg_type = msg.type if hasattr(msg, 'type') else 'text'
            
            # 处理语音消息：自动转换为文本（根据配置决定是否启用）
            enable_voice_to_text = True  # 默认启用
            if self.config:
                enable_voice_to_text = self.config.get("runtime", "enable_voice_to_text", True)
                
            if msg_type == "voice" and hasattr(msg, 'to_text') and enable_voice_to_text:
                try:
                    logger.info(f"🎤 [指定模式] 检测到语音消息，正在转换为文本...")
                    voice_text = msg.to_text()
                    if voice_text:
                        content = voice_text
                        msg_type = "text"  # 转换后按文本处理
                        logger.info(f"✅ [指定模式] 语音转文本成功: {content}")
                    else:
                        logger.warning("⚠️ [指定模式] 语音转文本失败，内容为空")
                        content = "[语音消息]"
                except Exception as voice_error:
                    logger.error(f"❌ [指定模式] 语音转文本失败: {voice_error}")
                    content = "[语音消息转换失败]"
            else:
                content = msg.content if hasattr(msg, 'content') else str(msg)
                if msg_type == "voice" and not enable_voice_to_text:
                    content = "[语音消息]"
            
            logger.info(f"   内容: {content}")
            logger.info(f"   消息类型: {msg_type}")
            logger.info(f"   时间: {datetime.now()}")
            
            # 更新消息计数 - 使用准确的nickname
            with self._lock:
                if nickname not in self.message_counts:
                    self.message_counts[nickname] = 0
                self.message_counts[nickname] += 1
                
            # 调用外部回调处理消息，传递准确的nickname和msg对象
            if self.on_message_callback:
                try:
                    # 在指定用户模式下，传递msg对象以便使用quote()方法回复
                    self.on_message_callback(nickname, content, msg_type, msg if self.listen_mode == "specific" else None)
                except Exception as e:
                    logger.error(f"❌ 消息处理回调执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"❌ 消息处理失败: {e}")
    
    
    
    def add_monitor_user(self, nickname: str) -> bool:
        """
        添加监控用户（仅在指定用户模式下有效）
        
        Args:
            nickname: 用户昵称
            
        Returns:
            bool: 是否添加成功
        """
        if self.listen_mode == "global":
            logger.warning("⚠️ 全局监听模式下无需指定监控用户")
            return True
            
        try:
            if not self.wx:
                logger.error("❌ 微信实例未初始化")
                return False
                
            # 使用lambda捕获nickname，以便在回调中知道是哪个用户
            callback_with_nickname = lambda msg, chat: self._on_message_received(msg, chat, nickname)
            
            # 添加监听
            self.wx.AddListenChat(nickname=nickname, callback=callback_with_nickname)
            
            with self._lock:
                if nickname not in self.monitor_users:
                    self.monitor_users.append(nickname)
                    self.message_counts[nickname] = 0
                    
            logger.info(f"✅ 成功添加用户 {nickname} 的监听")
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加用户 {nickname} 监听失败: {e}")
            return False
    
    def remove_monitor_user(self, nickname: str) -> bool:
        """
        移除监控用户（仅在指定用户模式下有效）
        
        Args:
            nickname: 用户昵称
            
        Returns:
            bool: 是否移除成功
        """
        if self.listen_mode == "global":
            logger.warning("⚠️ 全局监听模式下无法移除指定用户")
            return True
            
        try:
            if not self.wx:
                logger.error("❌ 微信实例未初始化")
                return False
                
            # 移除监听
            self.wx.RemoveListenChat(nickname=nickname)
            
            with self._lock:
                if nickname in self.monitor_users:
                    self.monitor_users.remove(nickname)
                    
            logger.info(f"✅ 成功移除用户 {nickname} 的监听")
            return True
            
        except Exception as e:
            logger.error(f"❌ 移除用户 {nickname} 监听失败: {e}")
            return False
    
    def start_listening(self) -> bool:
        """
        开始监听
        
        Returns:
            bool: 是否启动成功
        """
        try:
            if not self.wx:
                logger.error("❌ 微信实例未初始化")
                return False
                
            if self.is_listening:
                logger.warning("⚠️ 监听已在运行中")
                return True
            
            self.is_listening = True
            
            if self.listen_mode == "specific":
                # 指定用户监听模式 - 使用事件驱动AddListenChat
                self.wx.StartListening()
                # 启动保持运行的线程
                self.listener_thread = threading.Thread(target=self._keep_running, daemon=True)
                self.listener_thread.start()
                logger.info("🚀 指定用户监听已启动（使用AddListenChat事件驱动）")
                
            elif self.listen_mode == "global":
                # 全局监听模式 - 使用GetNextNewMessage轮询
                self._setup_global_listening()
                # 注意：全局模式不使用StartListening，因为我们用的是GetNextNewMessage
                logger.info("🌐 全局监听已启动（使用GetNextNewMessage轮询）")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动监听失败: {e}")
            self.is_listening = False
            return False
    
    def stop_listening(self) -> bool:
        """
        停止监听

        Returns:
            bool: 是否停止成功
        """
        try:
            if not self.is_listening:
                logger.warning("⚠️ 监听未在运行")
                return True

            # 设置停止标志
            self.is_listening = False

            if self.listen_mode == "specific":
                # 停止指定用户监听
                if self.wx:
                    self.wx.StopListening()

                # 等待监听线程结束
                if self.listener_thread and self.listener_thread.is_alive():
                    self.listener_thread.join(timeout=5)

            elif self.listen_mode == "global":
                # 停止COM全局监听
                self._cleanup_global_listening()

            logger.info(f"🛑 {self.listen_mode} 监听已停止")
            return True

        except Exception as e:
            logger.error(f"❌ 停止监听失败: {e}")
            return False
    
    def _setup_global_listening(self):
        """设置全局监听 - 使用带COM初始化的GetNextNewMessage全局监听机制"""
        try:
            # 检查是否支持GetNextNewMessage全局监听
            if hasattr(self.wx, 'GetNextNewMessage'):
                logger.info("🌐 使用带COM初始化的GetNextNewMessage设置全局监听...")

                # 启动全局监听线程（使用COM初始化）
                self.global_listener_thread = threading.Thread(target=self._com_global_message_listener, daemon=True)
                self.global_listener_thread.start()

                logger.info("🌐 COM全局监听设置完成，将监听所有新消息")
            else:
                logger.warning("⚠️ 微信实例不支持GetNextNewMessage，无法设置全局监听")

        except Exception as e:
            logger.error(f"❌ 设置全局监听失败: {e}")

    def _com_global_message_listener(self):
        """带COM初始化的全局消息监听线程函数"""
        logger.info("🧵 COM全局监听线程开始运行")

        # COM 初始化
        com_initialized = False
        try:
            pythoncom.CoInitialize()
            com_initialized = True
            logger.info("✅ COM环境初始化成功")
        except Exception as init_error:
            logger.error(f"❌ COM初始化失败: {init_error}")
            logger.warning("⚠️ 将在没有COM初始化的情况下继续运行")

        try:
            # 启动微信监听机制
            try:
                self.wx.StartListening()
                logger.info("✅ 微信监听机制已启动")
            except Exception as start_error:
                logger.warning(f"⚠️ 启动微信监听机制失败: {start_error}")

            # 获取配置信息
            check_interval = 30  # 默认30秒
            enable_voice_to_text = True  # 默认启用
            if self.config:
                check_interval = self.config.get("runtime", "global_message_check_interval", 30)
                enable_voice_to_text = self.config.get("runtime", "enable_voice_to_text", True)

            logger.info("🌐 开始全局消息监听...")
            logger.info(f"⚙️ 消息检查间隔: {check_interval}秒")
            logger.info(f"🎤 语音转文本: {'启用' if enable_voice_to_text else '禁用'}")
            last_message_time = {}  # 记录每个聊天的最后消息时间，用于去重
            
            while self.is_listening:
                try:
                    # 检查停止标志（更频繁的检查）
                    if not self.is_listening:
                        logger.info("🛑 检测到停止信号，退出全局监听循环")
                        break

                    # 检查微信实例是否仍然有效
                    if not self.wx:
                        logger.warning("⚠️ 微信实例已失效，退出全局监听循环")
                        break

                    # 使用GetNextNewMessage获取新消息
                    # 注意：这是一个阻塞调用，可能会长时间等待
                    logger.debug("🔍 正在等待新消息...")
                    message_data = self.wx.GetNextNewMessage()
                    
                    if message_data and isinstance(message_data, dict):
                        chat_name = message_data.get('chat_name', 'unknown')
                        chat_type = message_data.get('chat_type', 'unknown')
                        messages = message_data.get('msg', [])
                        
                        logger.info(f"🌐 接收到来自 {chat_name} ({chat_type}) 的 {len(messages)} 条消息")
                        
                        # 处理每条消息
                        for msg in messages:
                            try:
                                # 解析消息内容
                                content = ""
                                msg_type = "text"
                                sender = "unknown"
                                
                                if hasattr(msg, 'type'):
                                    msg_type = msg.type
                                
                                # 处理语音消息：自动转换为文本（使用全局配置）
                                if msg_type == "voice" and hasattr(msg, 'to_text') and enable_voice_to_text:
                                    try:
                                        logger.info(f"🎤 检测到语音消息，正在转换为文本...")
                                        voice_text = msg.to_text()
                                        if voice_text:
                                            content = voice_text
                                            msg_type = "text"  # 转换后按文本处理
                                            logger.info(f"✅ 语音转文本成功: {content}")
                                        else:
                                            logger.warning("⚠️ 语音转文本失败，内容为空")
                                            content = "[语音消息]"
                                    except Exception as voice_error:
                                        logger.error(f"❌ 语音转文本失败: {voice_error}")
                                        content = "[语音消息转换失败]"
                                elif hasattr(msg, 'content'):
                                    content = msg.content
                                elif hasattr(msg, 'text'):
                                    content = msg.text
                                else:
                                    content = str(msg)
                                    
                                # 如果是语音消息但未启用转文本功能
                                if msg_type == "voice" and not enable_voice_to_text:
                                    content = "[语音消息]"
                                
                                # 获取真实的消息发送者 - 根据官方文档正确识别
                                if hasattr(msg, 'sender') and msg.sender:
                                    sender = msg.sender
                                else:
                                    # 根据消息类型和聊天类型确定发送者
                                    if chat_type == 'friend':
                                        # 私聊：发送者就是聊天对象名称
                                        sender = chat_name
                                    elif chat_type == 'group':
                                        # 群聊：如果无法获取发送者，跳过此消息
                                        logger.warning(f"群聊消息缺少发送者信息，跳过处理: {content[:30]}...")
                                        continue
                                    else:
                                        # 其他类型聊天，使用聊天名称
                                        sender = chat_name
                                
                                # 严格的消息过滤逻辑
                                should_process = True
                                
                                # 1. 检查是否是机器人自己发送的消息
                                if hasattr(msg, 'attr') and msg.attr == 'self':
                                    should_process = False
                                    logger.debug(f"🤖 跳过自己发送的消息: {content[:30]}...")
                                elif self.wx and hasattr(self.wx, 'nickname') and sender == self.wx.nickname:
                                    should_process = False
                                    logger.debug(f"🤖 跳过机器人自己发送的消息: {content[:30]}...")
                                
                                # 2. 检查消息是否包含机器人回复的特征标识
                                if should_process and content:
                                    # 检查是否包含多种风格回复格式（说明是机器人生成的）
                                    style_patterns = ["（专业优雅型）", "（幽默热情型）", "（亲切自然型）", "（温馨关怀型）"]
                                    if any(pattern in content for pattern in style_patterns):
                                        should_process = False
                                        logger.info(f"🔄 跳过包含多风格格式的机器人回复: {content[:30]}...")
                                    
                                    # 检查其他机器人特征
                                    bot_keywords = ["😊", "🤖", "刘帅哥", "植发", "发际线", "（递纸巾）", "（摸摸头）"]
                                    if any(keyword in content for keyword in bot_keywords):
                                        should_process = False
                                        logger.debug(f"🔄 跳过疑似机器人回复的消息: {content[:30]}...")
                                
                                # 3. 额外的去重机制：如果消息内容与最近处理的相同，跳过
                                msg_key = f"{chat_name}:{content}"
                                current_time = time.time()
                                if msg_key in last_message_time:
                                    if current_time - last_message_time[msg_key] < 5:  # 5秒内的重复消息
                                        should_process = False
                                        logger.debug(f"🔄 跳过重复消息: {content[:30]}...")
                                
                                if should_process:
                                    # 记录处理时间
                                    last_message_time[msg_key] = current_time
                                    
                                    # 消息来源过滤：只处理私聊朋友消息，忽略群聊
                                    if hasattr(msg, 'attr') and msg.attr == 'friend' and chat_type == 'friend':
                                        # 只处理私聊朋友消息
                                        if self.on_message_callback:
                                            try:
                                                self.on_message_callback(sender, content, msg_type, msg)
                                                logger.info(f"✅ 处理私聊消息 - 发送者: {sender}")
                                            except Exception as cb_error:
                                                logger.error(f"❌ 消息回调处理失败: {cb_error}")
                                    else:
                                        # 跳过群聊消息和非朋友消息
                                        if chat_type == 'group':
                                            logger.debug(f"🚫 跳过群聊消息 - 群名: {chat_name}, 发送者: {sender}")
                                        else:
                                            logger.debug(f"🚫 跳过非朋友消息 (attr: {getattr(msg, 'attr', 'N/A')}, 发送者: {sender})")
                                    
                                    # 更新消息计数（使用真实发送者）
                                    with self._lock:
                                        if sender not in self.message_counts:
                                            self.message_counts[sender] = 0
                                            # 将新发送者加入监控列表
                                            if sender not in self.monitor_users:
                                                self.monitor_users.append(sender)
                                        
                                        self.message_counts[sender] += 1
                                    
                                    logger.info(f"🌐 全局监听处理消息 - 聊天: {chat_name}({chat_type}), 发送者: {sender}, 内容: {content[:50]}{'...' if len(content) > 50 else ''}")
                                    
                            except Exception as msg_error:
                                logger.error(f"❌ 处理单条消息失败: {msg_error}")
                    else:
                        # 没有新消息，根据配置等待后再次检查（降低CPU占用）
                        # 分段睡眠以便更快响应停止信号
                        sleep_time = 0
                        while sleep_time < check_interval and self.is_listening:
                            time.sleep(min(1, check_interval - sleep_time))  # 每秒检查一次停止信号
                            sleep_time += 1
                    
                except Exception as get_error:
                    logger.error(f"❌ 获取消息失败: {get_error}")
                    # 出错时也要检查停止标志
                    if not self.is_listening:
                        logger.info("🛑 检测到停止信号，退出全局监听循环（异常处理中）")
                        break

                    # 检查是否是因为停止监听导致的异常
                    error_msg = str(get_error).lower()
                    if any(keyword in error_msg for keyword in ['stop', 'close', 'disconnect', 'invalid']):
                        logger.info("🛑 检测到停止相关异常，可能是正常停止流程")
                        break

                    # 出错时短暂休眠，但要分段检查停止标志
                    for _ in range(10):  # 1秒分成10段，每100ms检查一次
                        if not self.is_listening:
                            logger.info("🛑 休眠期间检测到停止信号")
                            break
                        time.sleep(0.1)
                        
                except KeyboardInterrupt:
                    break
                    
        except Exception as e:
            logger.error(f"❌ COM全局监听线程异常: {e}")
        finally:
            # COM 清理
            if com_initialized:
                try:
                    pythoncom.CoUninitialize()
                    logger.info("✅ COM环境已清理")
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ COM清理失败: {cleanup_error}")

            logger.info("🔄 COM全局监听线程已结束")

    def _cleanup_global_listening(self):
        """清理全局监听设置"""
        try:
            logger.info("🛑 开始清理全局监听...")

            # 先设置停止标志
            self.is_listening = False
            logger.info("🚫 已设置停止标志 is_listening = False")

            # 调用微信的停止监听方法
            if self.wx:
                try:
                    self.wx.StopListening()
                    logger.info("✅ 已调用微信StopListening()方法")
                except Exception as stop_error:
                    logger.warning(f"⚠️ 调用StopListening()失败: {stop_error}")

            # 等待COM全局监听线程结束
            if hasattr(self, 'global_listener_thread') and self.global_listener_thread and self.global_listener_thread.is_alive():
                logger.info(f"⏳ 等待COM全局监听线程结束 (线程ID: {self.global_listener_thread.ident})...")

                # COM方案应该能更快响应停止信号
                for attempt in range(3):  # 减少尝试次数，因为COM方案应该更可靠
                    self.global_listener_thread.join(timeout=5)  # 给更多时间让COM清理
                    if not self.global_listener_thread.is_alive():
                        logger.info(f"⏹️ COM全局监听线程已停止 (尝试 {attempt + 1})")
                        break
                    else:
                        logger.warning(f"⚠️ 第 {attempt + 1} 次尝试：COM线程仍在运行...")
                        self.is_listening = False  # 再次确保停止标志已设置

                        # 再次调用StopListening
                        if self.wx:
                            try:
                                self.wx.StopListening()
                                logger.info("🔄 重新调用StopListening()尝试中断COM阻塞")
                            except Exception as retry_error:
                                logger.warning(f"⚠️ 重新调用StopListening()失败: {retry_error}")

                # 最终检查
                if self.global_listener_thread.is_alive():
                    logger.error("❌ COM全局监听线程未能正常停止")
                    logger.error("💡 这可能表明COM方案也无法解决问题")
                else:
                    logger.info("✅ COM全局监听线程已成功停止")
            else:
                logger.info("ℹ️ COM全局监听线程不存在或已停止")

            # 清理线程引用
            if hasattr(self, 'global_listener_thread'):
                self.global_listener_thread = None

            # 在全局模式下保留监控用户列表，因为用户可能想查看统计信息
            logger.info("🧹 全局监听清理完成")

        except Exception as e:
            logger.error(f"❌ 清理全局监听失败: {e}")

    def _keep_running(self):
        """保持监听运行的线程函数"""
        try:
            mode_name = "指定用户" if self.listen_mode == "specific" else "全局"
            logger.info(f"🔄 {mode_name}监听保持线程已启动")
            while self.is_listening:
                if self.wx:
                    time.sleep(1)  # 短暂休眠，避免过度占用CPU
                else:
                    break
                    
        except Exception as e:
            logger.error(f"❌ 监听保持线程异常: {e}")
        finally:
            mode_name = "指定用户" if self.listen_mode == "specific" else "全局"
            logger.info(f"🔄 {mode_name}监听保持线程已结束")
    

    
    def get_message_counts(self) -> Dict[str, int]:
        """
        获取各用户的消息计数
        
        Returns:
            Dict[str, int]: 用户名到消息计数的映射
        """
        with self._lock:
            return self.message_counts.copy()
    
    def get_monitor_users(self) -> List[str]:
        """
        获取监控用户列表
        
        Returns:
            List[str]: 监控用户列表
        """
        with self._lock:
            if self.listen_mode == "global":
                # 全局模式下返回所有发过消息的用户
                return list(self.message_counts.keys())
            else:
                return self.monitor_users.copy()
    
    def reset_message_count(self, nickname: str = None):
        """
        重置消息计数
        
        Args:
            nickname: 用户昵称，如果为None则重置所有计数
        """
        with self._lock:
            if nickname:
                if nickname in self.message_counts:
                    self.message_counts[nickname] = 0
                    logger.info(f"🔄 已重置用户 {nickname} 的消息计数")
            else:
                self.message_counts = {}
                logger.info("🔄 已重置所有用户的消息计数")
    
    def get_status(self) -> Dict:
        """
        获取监听器状态
        
        Returns:
            Dict: 状态信息
        """
        return {
            'is_listening': self.is_listening,
            'listen_mode': self.listen_mode,
            'monitor_users': self.get_monitor_users(),
            'message_counts': self.get_message_counts(),
            'wechat_initialized': self.wx is not None,
            'listener_thread_alive': self.listener_thread.is_alive() if self.listener_thread else False
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_listening()
            
            # 移除所有监听
            if self.wx and self.listen_mode == "specific":
                for user in self.monitor_users.copy():
                    self.remove_monitor_user(user)
                    
            self.wx = None
            logger.info("🧹 监听器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")

# 测试函数
def test_listener():
    """测试监听器功能"""
    def message_handler(sender, content, msg_type):
        print(f"处理消息 - 发送者: {sender}, 内容: {content}, 类型: {msg_type}")
    
    listener = WeChatListener(on_message_callback=message_handler)
    
    # 初始化
    if not listener.initialize_wechat():
        print("初始化失败")
        return
    
    # 测试指定用户模式
    print("测试指定用户模式...")
    listener.set_listen_mode("specific")
    listener.add_monitor_user("羽中蓝")
    listener.add_monitor_user("夜雨")
    
    # 测试全局模式
    print("切换到全局模式...")
    listener.set_listen_mode("global")
    
    # 开始监听
    if listener.start_listening():
        print("监听已启动，等待消息...")
        try:
            time.sleep(30)  # 监听30秒
        except KeyboardInterrupt:
            print("用户中断")
    
    # 停止监听
    listener.stop_listening()
    listener.cleanup()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
    test_listener() 