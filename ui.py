# -*- coding: utf-8 -*-
"""
用户界面模块
根据项目说明书要求，使用tkinter实现GUI，确保UI响应性和稳定的多线程通信
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import threading
import queue
import logging
import time
from datetime import datetime

# 导入其他项目模块
from config import Config
from llm_client_unified import LLMClientUnified
from smart_reply_handler_v2 import SmartReplyHandlerV2
from database_manager import DatabaseManager

# 定义LLM配置对话框类 (需要在SmartAssistantUI类之前定义，因为SmartAssistantUI会用到它)
class LLMConfigDialog:
    """LLM配置对话框"""
    def __init__(self, parent, title, llm_config=None):
        self.result = None
        self.top = tk.Toplevel(parent)
        self.top.title(title)
        self.top.transient(parent)
        self.top.grab_set()

        self.llm_config = llm_config if llm_config else {}
        
        self.create_widgets(self.llm_config)
        self.top.protocol("WM_DELETE_WINDOW", self.on_cancel)
        self.top.geometry("400x350") # 调整对话框大小

    def create_widgets(self, llm_config):
        # API Type
        ttk.Label(self.top, text="API类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_type_var = tk.StringVar(value=llm_config.get("type", "coze"))
        self.api_type_combo = ttk.Combobox(self.top, textvariable=self.api_type_var, width=30, state="readonly",
                     values=["coze", "openai", "siliconflow"])
        self.api_type_combo.grid(row=0, column=1, padx=5, pady=2)
        self.api_type_combo.bind("<<ComboboxSelected>>", self.on_api_type_changed)

        # LLM Name
        ttk.Label(self.top, text="名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.name_var = tk.StringVar(value=llm_config.get("name", ""))
        ttk.Entry(self.top, textvariable=self.name_var, width=30).grid(row=1, column=1, padx=5, pady=2)

        # API URL
        ttk.Label(self.top, text="API URL:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_url_var = tk.StringVar(value=llm_config.get("api_url", ""))
        ttk.Entry(self.top, textvariable=self.api_url_var, width=30).grid(row=2, column=1, padx=5, pady=2)

        # API Key
        ttk.Label(self.top, text="API Key:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.api_key_var = tk.StringVar(value=llm_config.get("api_key", ""))
        ttk.Entry(self.top, textvariable=self.api_key_var, show='*', width=30).grid(row=3, column=1, padx=5, pady=2) # 密码隐藏

        # Coze专有字段（动态显示）
        self.bot_id_label = ttk.Label(self.top, text="Bot ID:")
        self.bot_id_var = tk.StringVar(value=llm_config.get("bot_id", ""))
        self.bot_id_entry = ttk.Entry(self.top, textvariable=self.bot_id_var, width=30)

        self.user_id_label = ttk.Label(self.top, text="User ID:")
        self.user_id_var = tk.StringVar(value=llm_config.get("user_id", ""))
        self.user_id_entry = ttk.Entry(self.top, textvariable=self.user_id_var, width=30)

        # Model Name
        ttk.Label(self.top, text="模型名称:").grid(row=6, column=0, sticky=tk.W, padx=5, pady=2)
        self.model_name_var = tk.StringVar(value=llm_config.get("model_name", ""))
        ttk.Entry(self.top, textvariable=self.model_name_var, width=30).grid(row=6, column=1, padx=5, pady=2)
        
        # Description
        ttk.Label(self.top, text="描述:").grid(row=7, column=0, sticky=tk.W, padx=5, pady=2)
        self.description_var = tk.StringVar(value=llm_config.get("description", ""))
        ttk.Entry(self.top, textvariable=self.description_var, width=30).grid(row=7, column=1, padx=5, pady=2)

        # Buttons
        button_frame = ttk.Frame(self.top)
        button_frame.grid(row=8, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT, padx=5)

        # 初始化字段显示状态
        self.update_fields_visibility()

    def on_api_type_changed(self, event=None):
        """API类型切换事件处理"""
        self.update_fields_visibility()

    def update_fields_visibility(self):
        """根据API类型更新字段显示状态"""
        api_type = self.api_type_var.get()

        # 先隐藏所有Coze专有字段
        self.bot_id_label.grid_remove()
        self.bot_id_entry.grid_remove()
        self.user_id_label.grid_remove()
        self.user_id_entry.grid_remove()

        # 根据API类型显示相应字段
        if api_type == "coze":
            self.bot_id_label.grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
            self.bot_id_entry.grid(row=4, column=1, padx=5, pady=2)
            self.user_id_label.grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
            self.user_id_entry.grid(row=5, column=1, padx=5, pady=2)

    def on_ok(self):
        self.result = {
            "type": self.api_type_var.get(),
            "name": self.name_var.get(),
            "api_url": self.api_url_var.get(),
            "api_key": self.api_key_var.get(),
            "bot_id": self.bot_id_var.get(),
            "user_id": self.user_id_var.get(),
            "model_name": self.model_name_var.get(),
            "description": self.description_var.get()
        }
        self.top.destroy()

    def on_cancel(self):
        self.result = None
        self.top.destroy()


# 定义主UI类
class SmartAssistantUI:
    """智能微信助手用户界面"""
    
    def __init__(self):
        self.logger = logging.getLogger("UI")
        self.config = Config()
        
        # 初始化核心组件引用（在worker_thread_func中实际初始化）
        self.llm_client = None
        self.db_manager = None
        self.reply_handler = None
        self.wechat_manager = None # 用于停止监控时清理

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("wxauto智能微信助手")
        self.root.geometry("800x600")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 线程通信队列
        self.log_queue = queue.Queue()
        self.status_queue = queue.Queue()
        self.command_queue = queue.Queue()
        
        # 状态变量
        self.is_monitoring = False
        self.worker_thread = None
        
        # UI相关变量（在create_widgets之前初始化）
        self.listen_mode_var = tk.StringVar(value="specific")
        self.enable_smart_var = tk.BooleanVar(value=True)  # 默认启用智能处理

        # LLM配置相关UI变量
        self.current_llm_var = tk.StringVar()
        self.llm_combobox = None # 会在create_config_panel中创建
        self.current_llm_url_label = None
        self.current_llm_model_label = None
        self.current_llm_desc_label = None
        
        # 智能消息处理UI变量
        self.enable_smart_processing_var = tk.BooleanVar()
        self.merge_timeout_var = tk.DoubleVar()
        self.max_merge_count_var = tk.IntVar()
        self.smart_processing_status_label = None

        # 提示词UI变量
        self.persona_prompt_text = None

        # 监控用户列表UI变量
        self.user_listbox = None
        self.global_mode_tip = None
        self.list_frame = None
        self.user_button_frame = None
        self.current_mode_label = None # 监听模式状态标签

        # 状态显示标签字典
        self.status_labels = {}

        # 创建界面组件
        self.create_widgets()
        # 加载配置到UI（需要在所有UI组件创建后调用）
        self.load_config_to_ui()
        
        # 启动队列处理
        self.process_queues()

    # ==================== UI组件创建方法 ====================
    def create_widgets(self):
        """创建界面组件"""
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.create_control_panel(notebook)
        self.create_config_panel(notebook)
        self.create_log_panel(notebook)
    
    def create_control_panel(self, parent):
        """创建主控制面板"""
        control_frame = ttk.Frame(parent)
        parent.add(control_frame, text="主控制")
        
        # 微信配置区域
        wechat_config_frame = ttk.LabelFrame(control_frame, text="微信配置", padding=10)
        wechat_config_frame.pack(fill=tk.X, padx=5, pady=5)
        
        nickname_frame = ttk.Frame(wechat_config_frame)
        nickname_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(nickname_frame, text="监听用户昵称:", font=("", 9)).pack(side=tk.LEFT)
        self.wechat_nickname_var = tk.StringVar()
        ttk.Entry(nickname_frame, textvariable=self.wechat_nickname_var, width=20).pack(side=tk.LEFT, padx=(10, 5))
        
        ttk.Button(nickname_frame, text="更新配置", command=self.update_wechat_nickname).pack(side=tk.LEFT, padx=5)
        
        ttk.Label(wechat_config_frame, 
                              text="💡 提示：监听用户昵称应与发送消息的微信昵称一致", 
                              foreground="blue", font=("", 8)).pack(anchor=tk.W, pady=(5, 0))
        
        # 系统状态显示区域
        status_frame = ttk.LabelFrame(control_frame, text="系统状态", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        status_items = [
            ("微信连接", "wechat_status"),
            ("LLM连接", "llm_status"),
            ("监控状态", "monitor_status"),
            ("消息计数", "message_count")
        ]
        
        for i, (label_text, key) in enumerate(status_items):
            ttk.Label(status_frame, text=f"{label_text}:").grid(row=i//2, column=(i%2)*2, sticky=tk.W, padx=5, pady=2)
            self.status_labels[key] = ttk.Label(status_frame, text="未连接", foreground="red")
            self.status_labels[key].grid(row=i//2, column=(i%2)*2+1, sticky=tk.W, padx=5, pady=2)
        
        # 监听模式配置区域
        listen_mode_frame = ttk.LabelFrame(control_frame, text="监听模式", padding=10)
        listen_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        
        modes_info = [
            ("specific", "指定用户监听", "只监听添加到列表中的特定用户"),
            ("global", "全局监听", "监听所有微信对话，自动回复所有人")
        ]
        
        mode_buttons_frame = ttk.Frame(listen_mode_frame)
        mode_buttons_frame.pack(fill=tk.X, pady=5)
        
        for i, (mode_value, mode_name, mode_desc) in enumerate(modes_info):
            ttk.Radiobutton(
                mode_buttons_frame, 
                text=mode_name, 
                variable=self.listen_mode_var, 
                value=mode_value,
                command=self.on_listen_mode_change
            ).pack(side=tk.LEFT, padx=10)
            
        ttk.Label(listen_mode_frame, text="💡 指定用户监听：只回复列表中的用户 | 全局监听：自动回复所有微信对话", 
                              foreground="gray", font=("", 8)).pack(anchor=tk.W, pady=(0, 5))
                
        self.current_mode_label = ttk.Label(listen_mode_frame, text="当前模式: 指定用户监听", foreground="blue")
        self.current_mode_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 控制按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="启动监控", command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.restart_button = ttk.Button(button_frame, text="重启监控", command=self.restart_monitoring, state=tk.DISABLED)
        self.restart_button.pack(side=tk.LEFT, padx=5)
        
        # 监控用户列表区域
        user_frame = ttk.LabelFrame(control_frame, text="监控用户", padding=10)
        user_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.global_mode_tip = ttk.Label(
            user_frame, 
            text="🌐 全局监听模式：将自动监听所有微信对话，无需手动添加用户", 
            foreground="orange",
            font=("", 9)
        )
        
        self.user_button_frame = ttk.Frame(user_frame)
        self.user_button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)
        
        self.list_frame = ttk.Frame(user_frame)
        self.list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.user_listbox = tk.Listbox(self.list_frame, height=6)
        ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.user_listbox.yview).pack(side=tk.RIGHT, fill=tk.Y)
        self.user_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.user_listbox.configure(yscrollcommand=ttk.Scrollbar(self.list_frame, orient=tk.VERTICAL, command=self.user_listbox.yview).set)
        
        ttk.Button(self.user_button_frame, text="添加用户", command=self.add_user).pack(side=tk.LEFT, padx=2)
        ttk.Button(self.user_button_frame, text="删除用户", command=self.remove_user).pack(side=tk.LEFT, padx=2)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        config_frame = ttk.Frame(parent)
        parent.add(config_frame, text="配置")
        
        canvas = tk.Canvas(config_frame)
        scrollbar = ttk.Scrollbar(config_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 微信路径配置
        wechat_frame = ttk.LabelFrame(scrollable_frame, text="微信配置", padding=10)
        wechat_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(wechat_frame, text="微信路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        wechat_path_frame = ttk.Frame(wechat_frame)
        wechat_path_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        self.wechat_path_var = tk.StringVar()
        ttk.Entry(wechat_path_frame, textvariable=self.wechat_path_var, width=25).pack(side=tk.LEFT)
        ttk.Button(wechat_path_frame, text="浏览", command=self.browse_wechat_path).pack(side=tk.LEFT, padx=2)
        
        # LLM提供商配置
        llm_frame = ttk.LabelFrame(scrollable_frame, text="LLM提供商配置", padding=10)
        llm_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(llm_frame, text="当前LLM:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.current_llm_var = tk.StringVar()
        self.llm_combobox = ttk.Combobox(llm_frame, textvariable=self.current_llm_var, width=40, state="readonly")
        self.llm_combobox.grid(row=0, column=1, padx=5, pady=2)
        self.llm_combobox.bind("<<ComboboxSelected>>", self.on_llm_selected)
        
        ttk.Button(llm_frame, text="切换LLM", command=self.switch_llm).grid(row=0, column=2, padx=5, pady=2)
        
        llm_button_frame = ttk.Frame(llm_frame)
        llm_button_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        ttk.Button(llm_button_frame, text="添加LLM", command=self.add_llm).pack(side=tk.LEFT, padx=2)
        ttk.Button(llm_button_frame, text="编辑LLM", command=self.edit_llm).pack(side=tk.LEFT, padx=2)
        ttk.Button(llm_button_frame, text="删除LLM", command=self.delete_llm).pack(side=tk.LEFT, padx=2)
        ttk.Button(llm_button_frame, text="测试连接", command=self.test_llm_connection).pack(side=tk.LEFT, padx=2)
        
        # LLM信息显示区域
        llm_info_frame = ttk.Frame(llm_frame)
        llm_info_frame.grid(row=2, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        
        ttk.Label(llm_info_frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=1)
        self.current_llm_url_label = ttk.Label(llm_info_frame, text="未选择", foreground="gray")
        self.current_llm_url_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=1)
        
        ttk.Label(llm_info_frame, text="模型名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=1)
        self.current_llm_model_label = ttk.Label(llm_info_frame, text="未选择", foreground="gray")
        self.current_llm_model_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=1)
        
        ttk.Label(llm_info_frame, text="功能描述:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=1)
        self.current_llm_desc_label = ttk.Label(llm_info_frame, text="未选择", foreground="gray")
        self.current_llm_desc_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=1)
        
        # 提示词配置
        prompts_frame = ttk.LabelFrame(scrollable_frame, text="提示词配置", padding=10)
        prompts_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(prompts_frame, text="人设/系统提示词:").pack(anchor=tk.W, padx=5, pady=2)
        self.persona_prompt_text = scrolledtext.ScrolledText(prompts_frame, width=70, height=10, wrap=tk.WORD)
        self.persona_prompt_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=2)
        
        ttk.Button(prompts_frame, text="保存提示词", command=self.save_prompts_config).pack(pady=5)
        
        # 智能消息处理配置
        smart_processing_frame = ttk.LabelFrame(scrollable_frame, text="智能消息处理配置", padding=10)
        smart_processing_frame.pack(fill=tk.X, padx=5, pady=5)
        
        enable_smart_frame = ttk.Frame(smart_processing_frame)
        enable_smart_frame.pack(fill=tk.X, pady=5)
        
        self.enable_smart_processing_var = tk.BooleanVar()
        ttk.Checkbutton(
            enable_smart_frame, 
            text="启用智能消息处理功能", 
            variable=self.enable_smart_processing_var,
            command=self.on_smart_processing_config_change
        ).pack(side=tk.LEFT)
        
        ttk.Label(
            enable_smart_frame,
            text="智能合并用户连续消息，支持消息更正/补充，模拟自然对话节奏",
            foreground="gray",
            font=("", 8)
        ).pack(side=tk.LEFT, padx=(10, 0))
        
        merge_timeout_frame = ttk.Frame(smart_processing_frame)
        merge_timeout_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(merge_timeout_frame, text="消息合并延时:").pack(side=tk.LEFT)
        self.merge_timeout_var = tk.DoubleVar()
        ttk.Spinbox(
            merge_timeout_frame, 
            from_=1.0, 
            to=30.0, 
            increment=0.5,
            textvariable=self.merge_timeout_var,
            width=8,
            command=self.on_smart_processing_config_change
        ).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(merge_timeout_frame, text="秒").pack(side=tk.LEFT, padx=(2, 0))
        
        ttk.Label(
            merge_timeout_frame,
            text="(用户发送多条消息的等待合并时间，建议4-12秒)",
            foreground="gray",
            font=("", 8)
        ).pack(side=tk.LEFT, padx=(10, 0))
        
        max_merge_frame = ttk.Frame(smart_processing_frame)
        max_merge_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(max_merge_frame, text="最大合并数量:").pack(side=tk.LEFT)
        self.max_merge_count_var = tk.IntVar()
        ttk.Spinbox(
            max_merge_frame, 
            from_=3, 
            to=20, 
            increment=1,
            textvariable=self.max_merge_count_var,
            width=8,
            command=self.on_smart_processing_config_change
        ).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(max_merge_frame, text="条").pack(side=tk.LEFT, padx=(2, 0))
        
        ttk.Label(
            max_merge_frame,
            text="(超过此数量将立即处理，建议5-15条)",
            foreground="gray",
            font=("", 8)
        ).pack(side=tk.LEFT, padx=(10, 0))
        
        preset_frame = ttk.Frame(smart_processing_frame)
        preset_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(preset_frame, text="快速配置:").pack(side=tk.LEFT)
        
        preset_buttons = [
            ("快速响应 (4秒/5条)", 4.0, 5),
            ("平衡模式 (8秒/10条)", 8.0, 10),
            ("深度思考 (12秒/15条)", 12.0, 15)
        ]
        
        for preset_name, timeout, count in preset_buttons:
            ttk.Button(
                preset_frame, 
                text=preset_name,
                command=lambda t=timeout, c=count: self.apply_smart_preset(t, c)
            ).pack(side=tk.LEFT, padx=(5, 0))
        
        status_frame = ttk.Frame(smart_processing_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(status_frame, text="当前状态:").pack(side=tk.LEFT)
        self.smart_processing_status_label = ttk.Label(status_frame, text="已启用", foreground="green")
        self.smart_processing_status_label.pack(side=tk.LEFT, padx=(5, 0))

        ttk.Button(scrollable_frame, text="保存配置", command=self.save_config).pack(pady=10)
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.Frame(parent)
        parent.add(log_frame, text="日志")
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(log_button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_button_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=5)

    # ==================== 配置加载/保存方法 ====================    
    def load_config_to_ui(self):
        """将配置加载到UI"""
        self.wechat_nickname_var.set(self.config.get("wechat", "nickname") or "")
        self.wechat_path_var.set(self.config.get("wechat", "wechat_path") or "")
        
        self.load_llm_providers()
        
        saved_mode = self.config.get("wechat", "listen_mode") or "specific"
        self.listen_mode_var.set(saved_mode)
        self.update_ui_for_mode(saved_mode)
        
        self.load_smart_processing_config()
        self.load_prompts_config_to_ui()
        
        users = self.config.get("wechat", "monitored_users") or []
        for user in users:
            self.user_listbox.insert(tk.END, user)
    
    def save_config(self):
        """保存配置"""
        try:
            self.config.set("wechat", "nickname", self.wechat_nickname_var.get() or None)
            self.config.set("wechat", "wechat_path", self.wechat_path_var.get())
            
            self.save_current_llm_to_config()
            
            self.config.set("wechat", "listen_mode", self.listen_mode_var.get())
            
            users = [self.user_listbox.get(i) for i in range(self.user_listbox.size())]
            self.config.set("wechat", "monitored_users", users)
            
            self.save_smart_processing_config()
            self.save_prompts_config()
            
            if self.config.save_config():
                messagebox.showinfo("成功", "配置已保存")
                self.add_log("配置已保存")
            else:
                messagebox.showerror("错误", "配置保存失败")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}", exc_info=True)
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def save_user_list_to_config(self):
        """单独保存用户列表到配置文件（用于实时更新）"""
        try:
            users = [self.user_listbox.get(i) for i in range(self.user_listbox.size())]
            self.config.set("wechat", "monitored_users", users)
            self.config.save_config()
            self.add_log(f"监控用户列表已更新: {users}")
        except Exception as e:
            self.logger.error(f"保存用户列表失败: {e}", exc_info=True)
            messagebox.showerror("错误", f"保存用户列表失败: {e}")

    # ==================== 微信相关操作方法 ====================
    def update_wechat_nickname(self):
        """更新微信昵称配置"""
        try:
            nickname = self.wechat_nickname_var.get().strip()
            if not nickname:
                messagebox.showerror("错误", "请输入微信昵称")
                return
            
            self.config.set("wechat", "nickname", nickname)
            if self.config.save_config():
                self.add_log(f"✅ 微信昵称已更新为: {nickname}")
                messagebox.showinfo("成功", f"微信昵称已更新为: {nickname}")
                
                if self.is_monitoring:
                    self.add_log("🔄 正在重新初始化监听配置...")
                    self.restart_monitoring()
            else:
                messagebox.showerror("错误", "保存配置失败")
        except Exception as e:
            self.logger.error(f"更新微信昵称失败: {e}", exc_info=True)
            messagebox.showerror("错误", f"更新微信昵称失败: {e}")
    
    def browse_wechat_path(self):
        """浏览微信路径"""
        filename = filedialog.askopenfilename(
            title="选择微信程序",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.wechat_path_var.set(filename)
    
    def add_user(self):
        """添加监控用户"""
        user = simpledialog.askstring("添加用户", "请输入用户昵称:")
        if user and user.strip():
            user = user.strip()
            existing_users = [self.user_listbox.get(i) for i in range(self.user_listbox.size())]
            if user in existing_users:
                messagebox.showwarning("警告", f"用户 '{user}' 已存在")
                return
            
            self.user_listbox.insert(tk.END, user)
            self.save_user_list_to_config()
            
            if self.is_monitoring:
                self.command_queue.put(f"add_user:{user}")
                self.add_log(f"✅ 已添加监控用户: {user}")
    
    def remove_user(self):
        """删除监控用户"""
        selection = self.user_listbox.curselection()
        if selection:
            user = self.user_listbox.get(selection[0])
            self.user_listbox.delete(selection[0])
            self.save_user_list_to_config()
            
            if self.is_monitoring:
                self.command_queue.put(f"remove_user:{user}")
                self.add_log(f"❌ 已移除监控用户: {user}")
        else:
            messagebox.showinfo("提示", "请先选择要删除的用户")
    
    def start_monitoring(self):
        """启动监控"""
        if not self.is_monitoring:
            self.add_log("正在启动监控...")
            self.is_monitoring = True
            
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.restart_button.config(state=tk.NORMAL)
            
            self.worker_thread = threading.Thread(target=self.worker_thread_func, daemon=True)
            self.worker_thread.start()
            
            self.update_status("monitor_status", "运行中", "green")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.add_log("🛑 正在停止监控...")
            self.is_monitoring = False

            # 停止智能回复处理器（包含监听器）
            if hasattr(self, 'reply_handler') and self.reply_handler:
                try:
                    self.reply_handler.stop_monitoring()
                    self.add_log("🤖 智能回复处理器已停止")
                except Exception as e:
                    self.add_log(f"❌ 停止智能回复处理器失败: {e}")

            # 兼容性：如果还有旧的wechat_manager，也停止它
            if hasattr(self, 'wechat_manager') and self.wechat_manager:
                try:
                    self.wechat_manager.stop_monitoring()
                    self.add_log("📱 微信管理器已停止")
                except Exception as e:
                    self.add_log(f"❌ 停止微信管理器失败: {e}")
            
            self.wechat_manager = None
            self.llm_client = None
            self.db_manager = None
            self.reply_handler = None
            
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.restart_button.config(state=tk.DISABLED)
            
            self.update_status("monitor_status", "已停止", "red")
            self.update_status("wechat_status", "已断开", "red")
            self.update_status("llm_status", "已断开", "red")
            self.update_status("message_count", "未连接", "gray")
            
            self.add_log("✅ 监控已完全停止")
    
    def restart_monitoring(self):
        """重启监控"""
        self.add_log("🔄 正在重启监控...")
        self.stop_monitoring()
        time.sleep(1) # 等待停止完成
        self.start_monitoring()

    # ==================== 监听模式相关方法 ====================
    def on_listen_mode_change(self):
        """监听模式变更事件"""
        mode = self.listen_mode_var.get()
        self.update_ui_for_mode(mode)
        
        if self.is_monitoring:
            self.command_queue.put(f"switch_mode:{mode}")
            if mode == "specific":
                self.add_log("🔄 正在切换到指定用户监听模式...")
            else:
                self.add_log("🌐 正在切换到全局监听模式...")

    def update_ui_for_mode(self, mode):
        """根据监听模式更新UI显示"""
        if hasattr(self, 'current_mode_label') and self.current_mode_label:
            if mode == "specific":
                self.current_mode_label.config(text="当前模式: 指定用户监听", foreground="blue")
            elif mode == "global":
                self.current_mode_label.config(text="当前模式: 全局监听", foreground="green")
        
        if mode == "specific":
            if hasattr(self, 'global_mode_tip'):
                self.global_mode_tip.pack_forget()
            if hasattr(self, 'list_frame'):
                self.list_frame.pack(fill=tk.BOTH, expand=True)
            if hasattr(self, 'user_button_frame'):
                self.user_button_frame.pack(fill=tk.X, pady=5)
            
        elif mode == "global":
            if hasattr(self, 'list_frame'):
                self.list_frame.pack_forget()
            if hasattr(self, 'user_button_frame'):
                self.user_button_frame.pack_forget()
            if hasattr(self, 'global_mode_tip'):
                self.global_mode_tip.pack(fill=tk.X, pady=10)

    # ==================== LLM提供商管理方法 ====================
    def load_llm_providers(self):
        """从配置加载LLM提供商并更新UI"""
        providers = self.config.get("llm_providers", "providers") or []
        self.llm_combobox['values'] = [p['name'] for p in providers]
        if providers:
            self.llm_combobox.set(providers[0]['name']) # 默认选中第一个
            self.update_llm_info_display(providers[0])
        else:
            self.llm_combobox.set("")
            self.update_llm_info_display({})
            
        self.add_log(f"已加载 {len(providers)} 个LLM提供商配置")

    def update_llm_info_display(self, llm_config):
        """更新LLM信息显示"""
        if llm_config:
            api_url = llm_config.get('api_url', '')
            model_name = llm_config.get('model_name', '')
            desc = llm_config.get('description', '')
            # Coze专有字段
            bot_id = llm_config.get('bot_id', '')
            user_id = llm_config.get('user_id', '')
            info = api_url
            if bot_id:
                info += f" | Bot ID: {bot_id}"
            if user_id:
                info += f" | User ID: {user_id}"
            self.current_llm_url_label.config(text=info, foreground="black")
            self.current_llm_model_label.config(text=model_name, foreground="black")
            self.current_llm_desc_label.config(text=desc, foreground="black")
        else:
            self.current_llm_url_label.config(text="未选择", foreground="gray")
            self.current_llm_model_label.config(text="未选择", foreground="gray")
            self.current_llm_desc_label.config(text="未选择", foreground="gray")
    
    def on_llm_selected(self, event=None):
        """LLM选择事件"""
        selection = self.llm_combobox.current()
        if selection >= 0:
            providers = self.config.get("llm_providers", "providers") or []
            if selection < len(providers):
                selected_provider = providers[selection]
                self.update_llm_info_display(selected_provider)
    
    def switch_llm(self):
        """切换LLM提供商"""
        selection = self.llm_combobox.current()
        if selection >= 0:
            providers = self.config.get("llm_providers", "providers") or []
            if selection < len(providers):
                selected_provider = providers[selection]
                
                self.config.set("llm_providers", "current_provider", selected_provider['type'])
                
                if self.config.save_config():
                    self.add_log(f"✅ 已切换到LLM: {selected_provider['name']}")
                    messagebox.showinfo("成功", f"已切换到LLM: {selected_provider['name']}")
                    
                    if self.is_monitoring:
                        self.command_queue.put("reinit_llm")
                        self.add_log("🔄 正在重新初始化LLM客户端以应用新提供商配置...")
                else:
                    messagebox.showerror("错误", "保存配置失败")
        else:
            messagebox.showwarning("提示", "请先选择要切换的LLM提供商")
    
    def add_llm(self):
        """添加LLM提供商"""
        dialog = LLMConfigDialog(self.root, "添加LLM提供商")
        result = dialog.result
        
        if result:
            try:
                providers = self.config.get("llm_providers", "providers") or []
                
                for provider in providers:
                    if provider['type'] == result['type']:
                        messagebox.showerror("错误", f"LLM类型 {result['type']} 已存在")
                        return
                
                providers.append(result)
                self.config.set("llm_providers", "providers", providers)
                
                if self.config.save_config():
                    self.load_llm_providers() # 重新加载
                    self.add_log(f"✅ 已添加LLM提供商: {result['name']}")
                    messagebox.showinfo("成功", f"已添加LLM提供商: {result['name']}")
                else:
                    messagebox.showerror("错误", "保存配置失败")
                    
            except Exception as e:
                self.logger.error(f"添加LLM提供商失败: {e}", exc_info=True)
                messagebox.showerror("错误", f"添加LLM提供商失败: {e}")
    
    def edit_llm(self):
        """编辑LLM提供商"""
        selection = self.llm_combobox.current()
        if selection >= 0:
            providers = self.config.get("llm_providers", "providers") or []
            if selection < len(providers):
                current_provider = providers[selection]
                
                dialog = LLMConfigDialog(self.root, "编辑LLM提供商", current_provider)
                result = dialog.result
                
                if result:
                    try:
                        for i, provider in enumerate(providers):
                            if i != selection and provider['type'] == result['type']:
                                messagebox.showerror("错误", f"LLM类型 {result['type']} 已被其他提供商使用")
                                return
                        
                        providers[selection] = result
                        self.config.set("llm_providers", "providers", providers)
                        
                        if self.config.save_config():
                            self.load_llm_providers() # 重新加载
                            self.add_log(f"✅ 已更新LLM提供商: {result['name']}")
                            messagebox.showinfo("成功", f"已更新LLM提供商: {result['name']}")
                        else:
                            messagebox.showerror("错误", "保存配置失败")
                            
                    except Exception as e:
                        self.logger.error(f"编辑LLM提供商失败: {e}", exc_info=True)
                        messagebox.showerror("错误", f"编辑LLM提供商失败: {e}")
        else:
            messagebox.showwarning("提示", "请先选择要编辑的LLM提供商")
    
    def delete_llm(self):
        """删除LLM提供商"""
        selection = self.llm_combobox.current()
        if selection >= 0:
            providers = self.config.get("llm_providers", "providers") or []
            if selection < len(providers):
                provider_to_delete = providers[selection]
                
                if len(providers) <= 1:
                    messagebox.showerror("错误", "至少需要保留一个LLM提供商配置")
                    return
                
                if messagebox.askyesno("确认删除", f"确定要删除LLM提供商 '{provider_to_delete['name']}' 吗？"):
                    try:
                        del providers[selection]
                        self.config.set("llm_providers", "providers", providers)
                        
                        if self.config.save_config():
                            self.load_llm_providers() # 重新加载
                            self.add_log(f"❌ 已删除LLM提供商: {provider_to_delete['name']}")
                            messagebox.showinfo("成功", f"已删除LLM提供商: {provider_to_delete['name']}")
                        else:
                            messagebox.showerror("错误", "保存配置失败")
                            
                    except Exception as e:
                        self.logger.error(f"删除LLM提供商失败: {e}", exc_info=True)
                        messagebox.showerror("错误", f"删除LLM提供商失败: {e}")
        else:
            messagebox.showwarning("提示", "请先选择要删除的LLM提供商")
    
    def test_llm_connection(self):
        """测试LLM连接"""
        selection = self.llm_combobox.current()
        if selection >= 0:
            providers = self.config.get("llm_providers", "providers") or []
            if selection < len(providers):
                selected_provider = providers[selection]
                
                try:
                    temp_config = Config() # 使用新的Config实例
                    temp_config.config = self.config.config.copy() # 复制当前配置
                    temp_config.config['llm_providers']['current_provider'] = selected_provider['type'] # 设置测试提供商
                    
                    llm_client = LLMClientUnified(temp_config)
                    success, message = llm_client.test_connection()
                    
                    if success:
                        self.add_log(f"✅ LLM提供商 '{selected_provider['name']}' 连接测试成功")
                        messagebox.showinfo("测试成功", f"LLM提供商 '{selected_provider['name']}' 连接正常\n\n{message}")
                    else:
                        self.add_log(f"❌ LLM提供商 '{selected_provider['name']}' 连接测试失败: {message}")
                        messagebox.showerror("测试失败", f"LLM提供商 '{selected_provider['name']}' 连接失败\n\n{message}")
                        
                except Exception as e:
                    self.logger.error(f"测试LLM连接失败: {e}", exc_info=True)
                    messagebox.showerror("测试失败", f"测试LLM连接时发生错误: {e}")
        else:
            messagebox.showwarning("提示", "请先选择要测试的LLM提供商")
    
    def save_current_llm_to_config(self):
        """保存当前选中的LLM到配置"""
        try:
            selection = self.llm_combobox.current()
            if selection >= 0:
                providers = self.config.get("llm_providers", "providers") or []
                if selection < len(providers):
                    selected_provider = providers[selection]
                    self.config.set("llm_providers", "current_provider", selected_provider['type'])
        except Exception as e:
            self.logger.warning(f"保存当前LLM配置失败: {e}")

    # ==================== 智能消息处理相关方法 ====================
    def on_smart_processing_toggle(self):
        """智能处理功能开关事件（现已默认启用，此方法保留以确保兼容性）"""
        enabled = self.enable_smart_var.get()
        
        self.add_log(f"🎛️ 智能处理状态: enabled={enabled}, is_monitoring={self.is_monitoring}")
        
        if enabled:
            self.add_log("✅ 智能消息处理功能已启用")
        else:
            self.add_log("❌ 智能消息处理功能已禁用，使用标准处理模式")
        
        if self.is_monitoring:
            command = f"toggle_smart_processing:{enabled}"
            self.command_queue.put(command)
            self.add_log(f"📤 已发送命令到队列: {command}")
            if enabled:
                self.add_log("🔄 正在启用智能消息处理功能...")
            else:
                self.add_log("🔄 正在禁用智能消息处理功能...")
        else:
            self.add_log("ℹ️ 监控未运行，智能处理将在下次启动时默认启用")

    def load_smart_processing_config(self):
        """加载智能消息处理配置到UI"""
        try:
            enable_smart = self.config.get("smart_processing", "enable_smart_processing")
            if enable_smart is not None:
                self.enable_smart_processing_var.set(enable_smart)
            else:
                self.enable_smart_processing_var.set(True) # 默认启用
            
            merge_timeout = self.config.get("smart_processing", "merge_timeout")
            if merge_timeout is not None:
                self.merge_timeout_var.set(merge_timeout)
            else:
                self.merge_timeout_var.set(8.0) # 默认8秒
            
            max_merge_count = self.config.get("smart_processing", "max_merge_count")
            if max_merge_count is not None:
                self.max_merge_count_var.set(max_merge_count)
            else:
                self.max_merge_count_var.set(10) # 默认10条
            
            self.update_smart_processing_status()
            
        except Exception as e:
            self.logger.error(f"加载智能消息处理配置失败: {e}", exc_info=True)
    
    def save_smart_processing_config(self):
        """将智能消息处理配置保存到配置"""
        try:
            self.config.set("smart_processing", "enable_smart_processing", self.enable_smart_processing_var.get())
            self.config.set("smart_processing", "merge_timeout", self.merge_timeout_var.get())
            self.config.set("smart_processing", "max_merge_count", self.max_merge_count_var.get())
        except Exception as e:
            self.logger.error(f"保存智能消息处理配置失败: {e}", exc_info=True)
    
    def on_smart_processing_config_change(self):
        """智能消息处理配置变更回调"""
        try:
            self.update_smart_processing_status()
            
            if self.is_monitoring:
                config_data = {
                    "enable_smart_processing": self.enable_smart_processing_var.get(),
                    "merge_timeout": self.merge_timeout_var.get(),
                    "max_merge_count": self.max_merge_count_var.get()
                }
                self.command_queue.put(f"update_smart_config:{config_data}")
                self.add_log(f"📝 智能处理配置已更新: 延时{self.merge_timeout_var.get()}秒, 最大{self.max_merge_count_var.get()}条")
                
        except Exception as e:
            self.logger.error(f"智能处理配置变更处理失败: {e}", exc_info=True)
    
    def apply_smart_preset(self, timeout, count):
        """应用智能处理预设配置"""
        try:
            self.merge_timeout_var.set(timeout)
            self.max_merge_count_var.set(count)
            self.on_smart_processing_config_change()
            self.add_log(f"🎯 已应用预设配置: {timeout}秒/{count}条")
        except Exception as e:
            self.logger.error(f"应用预设配置失败: {e}", exc_info=True)
    
    def update_smart_processing_status(self):
        """更新智能处理状态显示"""
        # 确保llm_client在worker_thread_func中被正确初始化并赋值给self.llm_client
        if hasattr(self, 'llm_client') and self.llm_client:
            try:
                status = self.llm_client.get_status()
                llm_status = status.get("status", "未知")
                backend_type = status.get("backend_type", "未知")

                if llm_status == "已初始化":
                    self.smart_processing_status_label.config(text=f"状态: {backend_type} (已连接)", foreground="green")
                else:
                    self.smart_processing_status_label.config(text=f"状态: {backend_type} (未连接)", foreground="red")
            except Exception as e:
                self.logger.error(f"更新智能处理状态失败: {e}", exc_info=True)
                self.smart_processing_status_label.config(text="状态: 获取失败", foreground="red")
        else:
            self.smart_processing_status_label.config(text="状态: 未初始化LLM", foreground="gray")

    # ==================== 提示词管理方法 ====================
    def load_prompts_config_to_ui(self):
        """从配置加载提示词到UI"""
        persona_prompt = self.config.get("prompts", "persona_prompt", "")
        self.persona_prompt_text.delete(1.0, tk.END)
        self.persona_prompt_text.insert(tk.END, persona_prompt)
        self.logger.info("✅ 提示词配置已加载到UI")

    def save_prompts_config(self):
        """将UI中的提示词保存到配置"""
        new_persona_prompt = self.persona_prompt_text.get(1.0, tk.END).strip()
        self.config.set("prompts", "persona_prompt", new_persona_prompt)
        self.config.save_config()
        messagebox.showinfo("保存成功", "提示词配置已成功保存")
        self.logger.info("✅ 提示词配置已保存到config.json")

    # ==================== 日志与状态更新方法 ====================
    def update_message_count_from_handler(self, handler):
        """从处理器获取消息计数并更新UI"""
        try:
            counts = handler.get_message_counts()
            total_count = sum(counts.values())
            self.status_queue.put(("message_count", str(total_count), "green"))
        except Exception as e:
            self.logger.warning(f"更新消息计数失败: {e}")

    def update_status(self, key, text, color="black"):
        """线程安全地更新状态标签"""
        self.status_queue.put((key, text, color))
    
    def add_log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_queue.put(log_message)
    
    def process_queues(self):
        """处理队列消息"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
        except queue.Empty:
            pass
        
        try:
            while True:
                key, text, color = self.status_queue.get_nowait()
                if key in self.status_labels:
                    self.status_labels[key].config(text=text, foreground=color)
        except queue.Empty:
            pass
        
        self.root.after(100, self.process_queues)
    
    def clear_log(self):
        """清空日志文本框"""
        self.log_text.delete(1.0, tk.END)
    
    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("成功", "日志已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")
    
    def on_closing(self):
        """关闭程序时的处理"""
        if self.is_monitoring:
            if messagebox.askokcancel("退出", "监控正在运行，确定要退出吗？"):
                self.stop_monitoring()
                self.root.after(1000, self.root.destroy)
        else:
            self.root.destroy()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

    # ==================== 后台工作线程 ====================
    def worker_thread_func(self):
        """后台工作线程，运行所有核心逻辑"""
        try:
            self.logger.info("后台工作线程启动")
            
            # 1. 初始化核心组件
            # LLMClientUnified和DatabaseManager已在文件开头导入
            
            self.db_manager = DatabaseManager(self.config)
            self.add_log("✅ 数据库管理器初始化成功")

            # 将llm_client初始化并赋值给self.llm_client，以便其他方法访问
            self.llm_client = LLMClientUnified(self.config)
            self.add_log("✅ 统一LLM客户端初始化成功")
            
            # 测试LLM连接 - 已注释以加快启动速度
            # try:
            #     success, message = self.llm_client.test_connection()
            #     if success:
            #         self.update_status("llm_status", "已连接", "green")
            #         self.add_log(f"✅ LLM连接测试成功: {message}")
            #     else:
            #         self.update_status("llm_status", "连接失败", "red")
            #         self.add_log(f"❌ LLM连接测试失败: {message}")
            # except Exception as e:
            #     self.update_status("llm_status", "连接异常", "red")
            #     self.add_log(f"❌ LLM连接测试异常: {e}")
            
            # 跳过LLM连接测试，直接设置为待检测状态
            self.update_status("llm_status", "跳过检测", "orange")
            self.add_log("ℹ️ 已跳过LLM连接测试，加快启动速度")

            # SmartReplyHandlerV2 内部管理 WeChatListener
            self.reply_handler = SmartReplyHandlerV2(llm_client=self.llm_client, database_manager=self.db_manager) 
            
            # 启动智能回复处理器
            try:
                if not self.reply_handler.initialize(): 
                    self.add_log("错误: 智能回复处理器初始化失败")
                    self.update_status("wechat_status", "初始化失败", "red")
                    self.is_monitoring = False
                    return
                else:
                    self.add_log("成功: 智能回复处理器初始化成功")
                    wechat_instance = self.reply_handler.listener.get_wechat_instance() 
                    if wechat_instance:
                        self.update_status("wechat_status", "已连接", "green")
                    else:
                        self.status_queue.put(("wechat_status", "连接失败", "red"))

            except Exception as e:
                self.add_log(f"错误: 启动处理器时发生异常: {e}")
                self.is_monitoring = False
            
            self.add_log("✅ 智能回复处理器初始化成功")
            self.update_status("wechat_status", "已连接", "green")
            

            # 根据UI设置监听模式
            current_mode = self.listen_mode_var.get()
            if self.reply_handler.listener.set_listen_mode(current_mode): 
                self.add_log(f"✅ 已设置监听模式为: {current_mode}")
                if current_mode == "global":
                    self.add_log("🌐 全局监听模式：将自动监听所有微信对话")
                else:
                    self.add_log("👥 指定用户监听模式：只监听添加的用户")
            else:
                self.add_log(f"❌ 设置监听模式失败: {current_mode}")
            
            # 添加监控用户（仅在指定用户模式下）
            if current_mode == "specific":
                users = [self.user_listbox.get(i) for i in range(self.user_listbox.size())]
                if not users:
                    self.add_log("⚠️ 指定用户模式下监控列表为空，请在主控制面板添加用户")
                else:
                    self.add_log(f"📋 从UI加载监控用户列表: {users}")
                    for user in users:
                        success = self.reply_handler.add_monitor_user(user) 
                        if success:
                            self.add_log(f"✅ 已成功添加监控用户: [{user}]")
                        else:
                            self.add_log(f"❌ 添加监控用户失败: [{user}]，请检查日志获取详细错误信息。")
            else:
                self.add_log("🌐 全局监听模式：无需手动添加监控用户")

            # 启动监控
            if self.reply_handler.start_monitoring(): 
                self.add_log("🚀 监控已启动")
                self.update_status("monitor_status", "运行中", "green")
            else:
                self.add_log("❌ 启动监控失败")
                self.update_status("monitor_status", "已停止", "red")
                return

            # 循环等待停止指令
            while self.is_monitoring:
                try:
                    command = self.command_queue.get_nowait()
                    if command == "stop":
                        break
                    elif command.startswith("add_user:"):
                        user = command.split(":", 1)[1]
                        self.reply_handler.add_monitor_user(user) 
                        self.add_log(f"实时添加用户: {user}")
                    elif command.startswith("remove_user:"):
                        user = command.split(":", 1)[1]
                        self.reply_handler.remove_monitor_user(user) 
                        self.add_log(f"实时移除用户: {user}")
                    elif command.startswith("switch_mode:"):
                        mode = command.split(":", 1)[1]
                        try:
                            self.add_log(f"🔄 正在切换监听模式到: {mode}")
                            
                            if self.reply_handler.listener.set_listen_mode(mode): 
                                if mode == "specific":
                                    self.add_log("✅ 已切换到指定用户监听模式")
                                    users = [self.user_listbox.get(i) for i in range(self.user_listbox.size())]
                                    for user in users:
                                        self.reply_handler.add_monitor_user(user) 
                                        self.add_log(f"📋 重新添加监控用户: {user}")
                                elif mode == "global":
                                    self.add_log("✅ 已切换到全局监听模式，将自动监听所有对话")
                            else:
                                self.add_log("❌ 监听模式切换失败")
                                
                        except Exception as e:
                            self.add_log(f"❌ 切换监听模式失败: {e}")
                    elif command == "reinit_llm":
                        try:
                            self.add_log(f"🔄 正在重新初始化LLM客户端...")
                            
                            self.config.load_config()
                            
                            new_llm_client = LLMClientUnified(self.config)
                            
                            success, message = new_llm_client.test_connection()
                            if success:
                                self.reply_handler.llm_client = new_llm_client 
                                self.llm_client = new_llm_client 
                                self.add_log(f"✅ LLM客户端重新初始化成功: {message}")
                                self.update_status("llm_status", "已连接", "green")
                            else:
                                self.add_log(f"❌ 新LLM客户端连接测试失败: {message}")
                                self.update_status("llm_status", "连接失败", "red")
                                
                        except Exception as e:
                            self.add_log(f"❌ 重新初始化LLM客户端失败: {e}")
                            self.update_status("llm_status", "初始化失败", "red")
                    elif command.startswith("toggle_smart_processing:"):
                        try:
                            enabled = command.split(":", 1)[1] == "True"
                            self.add_log(f"🔄 正在处理智能消息开关命令: {command} -> enabled={enabled}")
                            self.reply_handler.enable_smart_message_processing(enabled) 
                            if enabled:
                                self.add_log("✅ 智能消息处理功能已启用")
                            else:
                                self.add_log("❌ 智能消息处理功能已禁用，使用标准处理模式")
                        except Exception as e:
                            self.add_log(f"❌ 切换智能处理功能失败: {e}")
                            import traceback
                            self.add_log(f"详细错误: {traceback.format_exc()}")
                    elif command.startswith("update_smart_config:"):
                        try:
                            import ast
                            config_str = command.split(":", 1)[1]
                            config_data = ast.literal_eval(config_str)
                            self.add_log(f"🔧 正在更新智能处理配置: {config_data}")
                            self.reply_handler.update_smart_processing_config(config_data) 
                            self.add_log("✅ 智能处理配置已动态更新")
                        except Exception as e:
                            self.add_log(f"❌ 更新智能处理配置失败: {e}")
                            import traceback
                            self.add_log(f"详细错误: {traceback.format_exc()}")

                except queue.Empty:
                    pass
                
                self.update_message_count_from_handler(self.reply_handler) 
                self.update_smart_processing_status() 
                time.sleep(20) 

            self.add_log("正在停止监控...")
            if self.reply_handler:
                self.reply_handler.stop_monitoring() 
                self.reply_handler.cleanup() 
            self.add_log("监控已停止，资源已清理。")

        except Exception as e:
            self.logger.error(f"后台线程发生错误: {e}", exc_info=True)
            self.add_log(f"❌ 后台线程错误: {e}")
        finally:
            self.is_monitoring = False
            self.update_status("monitor_status", "已停止", "red")
