
# 微信智能助手 - 生产环境部署检查清单

## 📋 部署前检查

### 1. 环境准备
- [ ] Python 3.7+ 已安装
- [ ] 必要依赖已安装 (pip install -r requirements.txt)
- [ ] 微信PC版已安装并登录

### 2. 配置文件设置
- [ ] 将 config_example.json 重命名为 config.json
- [ ] 配置扣子API密钥 (coze_pat_token)
- [ ] 配置Bot ID (coze_bot_id)
- [ ] 配置通义千问API (如需使用)

### 3. 功能验证
- [ ] 运行 python main.py 启动程序
- [ ] 界面正常显示
- [ ] 微信连接成功
- [ ] 添加监控用户测试
- [ ] 发送测试消息验证智能回复

### 4. 生产环境优化
- [ ] 检查日志级别设置
- [ ] 配置自动启动 (如需要)
- [ ] 设置监控和告警 (如需要)

## 🎯 核心功能特性
- ✅ 智能消息合并 (8秒收集期)
- ✅ 消息更正识别
- ✅ 指定用户/全局监听模式  
- ✅ 多LLM支持 (扣子/通义千问)
- ✅ 数据库存储聊天记录
- ✅ 现代化UI界面

## 📞 技术支持
如遇问题请参考:
- WXAUTO_API_DOC.md - 完整技术文档
- README.md - 项目说明
- DEPLOY_GUIDE.md - 部署指南
