# -*- coding: utf-8 -*-
"""
Coze LLM 客户端（仅支持非流式）
适配 Coze 平台 API
"""
import requests
import logging
import time
from typing import Optional, Dict, Any

class CozeLLMClient:
    def __init__(self, api_key: str, bot_id: str, user_id: str, api_url: str = "https://api.coze.cn/open_api/v2/chat"):
        self.api_key = api_key
        self.bot_id = bot_id
        self.user_id = user_id
        self.api_url = api_url
        self.logger = logging.getLogger("CozeLLMClient")
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "api.coze.cn",
            "Connection": "keep-alive"
        })

    def chat(self, message: str, parameters: Optional[Dict[str, Any]] = None, system_prompt: Optional[str] = None, user_id: Optional[str] = None) -> str:
        # 构造消息体
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        # 为每个用户生成唯一的user_id，避免对话历史混乱
        import hashlib
        if user_id:
            # 使用用户名生成唯一ID
            unique_user_id = hashlib.md5(f"wxauto_{user_id}".encode()).hexdigest()[:12]
        else:
            unique_user_id = self.user_id
            
        # v3 API 使用正确的参数格式
        data = {
            "bot_id": self.bot_id,
            "user_id": unique_user_id,
            "stream": False,
            "auto_save_history": True,
            "additional_messages": messages,
            "parameters": parameters or {}
        }
        try:
            self.logger.info(f"[Coze] 请求体: {data}")
            resp = self.session.post(self.api_url, json=data, timeout=30)
            self.logger.info(f"[Coze] POST {self.api_url} 返回状态: {resp.status_code}")
            self.logger.info(f"[Coze] POST返回内容: {resp.text}")
            resp.raise_for_status()
            resp_json = resp.json()
            self.logger.info(f"[Coze] 完整响应: {resp_json}")

            # 检查是否有错误
            if "code" in resp_json and resp_json["code"] != 0:
                error_msg = resp_json.get("msg", "未知错误")
                self.logger.error(f"[Coze] API返回错误: {error_msg}")
                return f"[Coze] API错误: {error_msg}"

            # v3 API 响应处理
            conversation_id = resp_json.get("data", {}).get("conversation_id")
            chat_id = resp_json.get("data", {}).get("id")
            self.logger.info(f"[Coze] conversation_id: {conversation_id}, chat_id: {chat_id}")

            if not conversation_id or not chat_id:
                return f"[Coze] 无法获取conversation_id或chat_id: {resp_json}"

            # 轮询retrieve接口直到status==completed
            retrieve_url = f"https://api.coze.cn/v3/chat/retrieve?conversation_id={conversation_id}&chat_id={chat_id}"
            for i in range(30):
                retrieve_resp = self.session.get(retrieve_url, timeout=10)
                self.logger.info(f"[Coze] GET {retrieve_url} 第{i+1}次 返回: {retrieve_resp.text}")
                retrieve_json = retrieve_resp.json()
                status = retrieve_json.get("data", {}).get("status", "")
                if status == "completed":
                    break
                time.sleep(1)

            # 获取最终消息内容
            message_url = f"https://api.coze.cn/v3/chat/message/list?chat_id={chat_id}&conversation_id={conversation_id}"
            message_resp = self.session.get(message_url, timeout=10)
            self.logger.info(f"[Coze] GET {message_url} 返回: {message_resp.text}")
            message_json = message_resp.json()

            # 提取AI回复
            for msg in message_json.get("data", []):
                if msg.get("role") == "assistant" and msg.get("type") == "answer":
                    self.logger.info(f"[Coze] AI回复: {msg.get('content','')}")
                    return msg.get("content", "")

            self.logger.warning("[Coze] 未获取到AI回复")
            return "[Coze] 未获取到AI回复"
        except Exception as e:
            self.logger.error(f"Coze API请求失败: {e}", exc_info=True)
            return f"Coze API请求失败: {e}"

    def generate_response(self, messages, temperature=0.7, num_choices=1, user_id=None):
        # 只取第一个用户消息
        user_message = ""
        for msg in messages:
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break
        uid = user_id or self.user_id
        return [self.chat(user_message, user_id=uid)]

    def get_status(self):
        return {"status": "已初始化", "backend_type": "Coze"}

    def test_connection(self) -> tuple:
        reply = self.chat("你好，测试Coze连通性")
        if "Coze API请求失败" not in reply and reply.strip() != "":
            return True, "Coze连通性正常"
        else:
            return False, reply

if __name__ == "__main__":
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s %(levelname)s %(name)s %(message)s"
    )
    api_key = "sat_GOoLufsUbZxhLQp1RkaySNzHlOwEd23zHv2MQgv8S9rdyk8wTc3nltgizttZmi4z"
    bot_id = "7526426421582118950"
    user_id = "test_user"
    client = CozeLLMClient(api_key=api_key, bot_id=bot_id, user_id=user_id)
    print("[非流式] 测试: ", client.chat("hello")) 