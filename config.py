# -*- coding: utf-8 -*-
"""
配置管理模块
根据项目说明书要求，实现配置化管理，避免硬编码
"""

import os
import json
import logging

class Config:
    """项目配置管理类"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.logger = logging.getLogger("Config")
        
        # 默认配置
        self.default_config = {
            # 微信相关配置
            "wechat": {
                "nickname": None,  # 微信昵称，None表示使用默认微信
                "wechat_path": "",  # 微信客户端路径
                "monitored_users": [],  # 监控的用户列表
                "auto_start_wechat": True  # 是否自动启动微信
            },
            
            # LLM API 配置
            "llm": {
                "api_url": "",
                "api_key": "",
                "model_name": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_retries": 5,
                "initial_delay": 1,
                "timeout": 60,
                "num_choices": 0  # 生成的备选回复数量
            },
            
            # 数据库配置
            "database": {
                "db_path": "chat_archive.db",
                "knowledge_path": "knowledge.txt",
                "backup_interval": 3600  # 备份间隔（秒）
            },
            
            # 运行时配置
            "runtime": {
                "check_interval": 2,  # 微信监控检查间隔（秒）
                "global_message_check_interval": 30,  # 全局监听模式消息检查间隔（秒）
                "archive_interval": 300,  # 聊天存档间隔（秒）
                "history_limit": 10,  # 上下文历史消息数量
                "enable_debug": True,  # 是否启用调试模式
                "enable_voice_to_text": True  # 是否启用语音转文本功能
            },
            
            # 智能消息处理配置
            "smart_processing": {
                "enable_smart_processing": True,  # 是否启用智能消息处理
                "merge_timeout": 8.0,  # 消息合并等待时间（秒）
                "max_merge_count": 10,  # 最大合并消息数量
                "response_delay_single": [0.5, 0.8],  # 单条消息响应延迟范围（秒）
                "response_delay_multi": [1.0, 2.5],   # 多条消息响应延迟范围（秒）
                "correction_keywords": [  # 更正关键词列表
                    "不对", "错了", "不是", "应该是", "我是说", "我说的是",
                    "更正", "纠正", "改正", "修改", "搞错了", "打错了"
                ]
            },
            
            # LLM提供商配置
            "llm_providers": {
                "current_provider": "coze",  # 当前使用的提供商
                "providers": [
                    {
                        "name": "Coze 扣子",
                        "type": "coze",
                        "api_url": "https://api.coze.cn/v3/chat",
                        "api_key": "sat_GOoLufsUbZxhLQp1RkaySNzHv2MQgv8S9rdyk8wTc3nltgizttZmi4z",
                        "bot_id": "7526426421582118950",
                        "user_id": "default_user",  # 默认用户ID，实际使用时会根据微信用户动态生成
                        "description": "Coze 扣子大模型"
                    }
                ]
            },
            
            # 延时策略配置
            "delay_strategies": {
                "current_strategy": "instant",  # 当前使用的延时策略
                "strategies": [
                    {
                        "name": "立即发送",
                        "type": "instant",
                        "description": "适合故事类机器人，零延时，最佳听故事体验",
                        "typing_delay": {
                            "base_time": 0.0,      # 基础延时（秒）
                            "char_time": 0.0,      # 每字符延时（秒）
                            "max_time": 0.0        # 最大延时（秒）
                        }
                    },
                    {
                        "name": "快速响应",
                        "type": "fast",
                        "description": "适合客服机器人，极简延时，快速响应",
                        "typing_delay": {
                            "base_time": 0.2,      # 基础延时（秒）
                            "char_time": 0.01,     # 每字符延时（秒）
                            "max_time": 1.5        # 最大延时（秒）
                        }
                    },
                    {
                        "name": "自然对话",
                        "type": "natural",
                        "description": "适合日常聊天机器人，模拟真人打字速度",
                        "typing_delay": {
                            "base_time": 0.5,      # 基础延时（秒）
                            "char_time": 0.03,     # 每字符延时（秒）
                            "max_time": 4.0        # 最大延时（秒）
                        }
                    },
                    {
                        "name": "慢速回复",
                        "type": "slow",
                        "description": "适合深度思考类机器人，模拟仔细思考过程",
                        "typing_delay": {
                            "base_time": 1.0,      # 基础延时（秒）
                            "char_time": 0.05,     # 每字符延时（秒）
                            "max_time": 8.0        # 最大延时（秒）
                        }
                    }
                ]
            },
            
            # FastGPT 配置
            # "fastgpt": {
            #     "api_url": "",  # FastGPT 线上 API 地址
            #     "api_key": ""   # FastGPT 应用的 API Key
            # }
        }
        
        # 提示词配置
        self.default_config["prompts"] = {
            "persona_prompt": "" ""
        }
        
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有必要的键都存在
                    return self._merge_config(self.default_config, config)
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                return self.default_config.copy()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}", exc_info=True)
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            self.logger.info("配置已保存")
            return True
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}", exc_info=True)
            return False
    
    def _merge_config(self, default, user):
        """递归合并配置，确保用户配置包含所有默认键"""
        merged = default.copy()
        for key, value in user.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key] = self._merge_config(merged[key], value)
            else:
                merged[key] = value
        return merged
    
    def get(self, section, key, default=None):
        """获取配置值"""
        try:
            return self.config.get(section, {}).get(key, default)
        except Exception:
            return default
    
    def set(self, section, key, value):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value 